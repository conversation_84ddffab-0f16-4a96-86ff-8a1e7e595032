#ifndef __XBOTGO_SDK_CORE_LOGIC_BASKETABLLSINGLESTRATEGY_H__
#define __XBOTGO_SDK_CORE_LOGIC_BASKETABLLSINGLESTRATEGY_H__
#include <iostream>
#include <memory>
#include "SingleStrategy.h"
namespace XbotgoSDK
{

class BasketballSingleStrategy
{
public:
    BasketballSingleStrategy(){}
	~BasketballSingleStrategy(){}
 
    // int track (const std::vector<YOLODetectionResult>& detectResult)override {
    //     return 0;
    // }

    // void targetInit(const std::vector<DetectionResult>& detectResults, const DetectionResult& target)override {
    //     return ;
    // }

    // protected:

    // int tryToDoTragetInit(const std::vector<YOLODetectionResult>& detectResult)override{
    //     return 0;
    // }

    // // int tryToDoTragetInit(const std::vector<JerseyNumberDetectionResult>& detectResult)override{
    // //     return 0;
    // // }

    // int handleSOTResult(const std::vector<YOLODetectionResult>& detectResult, int index)override {
    //     return 0;
    // }

    // // int handleSOTResult(std::vector<JerseyNumberDetectionResult>& detectResult, int index)override{
    // //     return 0;
    // // }

    // int doFullFeatureNetFound(const std::vector<YOLODetectionResult>& detectResult, int index)override {
    //     return 0;
    // }

    // // int doFullFeatureNetFound(std::vector<JerseyNumberDetectionResult>& detectResult, int index)override{
    // //     return 0;
    // // }

    // void zoom(const std::vector<YOLODetectionResult>& detectResult, int index, double currentZoomValue)override {
    //     return ;
    // }

    // void control(const std::vector<YOLODetectionResult>& detectResult, int index, double currentZoomValue)override {
    //     return ;
    // }

    
};

}

#endif
# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html.
# For more examples on how to use CMake, see https://github.com/android/ndk-samples.

# Sets the minimum CMake version required for this project.
cmake_minimum_required(VERSION 3.18.1)

# Declares and names the project.

project("myapplication")

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# <PERSON>radle automatically packages shared libraries with your APK.
#
# In this top level CMakeLists.txt, ${CMAKE_PROJECT_NAME} is used to define
# the target library name; in the sub-module's CMakeLists.txt, ${PROJECT_NAME}
# is preferred for the same purpose.
#
# In order to load a library into your app from Java/Kotlin, you must call
# System.loadLibrary() and pass the name of the library defined here;
# for GameActivity/NativeActivity derived applications, the same library name must be
# used in the AndroidManifest.xml file.
include_directories(
        ${CMAKE_SOURCE_DIR}/include/
#        ${CMAKE_SOURCE_DIR}/include/ByteTrack/eigen-3.3.7
        ${CMAKE_SOURCE_DIR}/include/Module/ByteTrack/Eigen
)

add_library(${CMAKE_PROJECT_NAME} SHARED
        # List C/C++ source files with relative paths to this CMakeLists.txt.
        JxrApi.cpp
        main.cpp
        include/Module/ByteTrack/BYTETracker.cpp
        include/Module/ByteTrack/KalmanFilter.cpp
        include/Module/ByteTrack/lapjv.cpp
        include/Module/ByteTrack/Object.cpp
        include/Module/ByteTrack/Rect.cpp
        include/Module/ByteTrack/STrack.cpp
        include/Module/ByteTacker.cpp
        include/Module/DebugLogManager.cpp
        include/Module/FeatureExtractor.cpp
        include/Module/FeatureNet.cpp
        include/Strategy/BRTracker.cpp
        include/Strategy/MOT.cpp
        include/Strategy/SingleZoom.cpp
        include/Strategy/SmartTracker.cpp
        include/Control/Gimbal/AngleManager.cpp
        include/Control/Gimbal/Controller.cpp
        include/Control/Gimbal/Kalman_Filter.cpp
        include/Control/Gimbal/MatrixControl.cpp
        include/Control/Gimbal/OneOrderLowPassFilter.cpp
        gimbal_mqtt_jni.cpp
        gimbal_mqtt_message.c
        include/Control/Gimbal/PIDController.cpp
        include/Control/Gimbal/PixelMapAngle.cpp)
# Specifies libraries CMake should link to your target library. You
# can link libraries from various origins, such as libraries defined in this
# build script, prebuilt third-party libraries, or Android system libraries.

add_library("ai_edit" SHARED AIEditProcessor.cpp ai_edit.cpp)
target_link_libraries(${CMAKE_PROJECT_NAME}
        # List libraries link to the target library
        android
        log)
target_link_libraries("ai_edit"
        # List libraries link to the target library
        android
        log)

#define         _CRT_SECURE_NO_WARNINGS         1

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "gimbal_mqtt_message.h"

int MotorCtrlInfoLength(void)
{
    return 6;
}

int MotorCtrlInfoPack(const struct MotorCtrlInfo* ctrlInfo, uint8_t* infoArray)
{
    if (!infoArray)
    {
        printf("info array is NULL!\n");
        return -1;
    }

    if (!ctrlInfo)
    {
        printf("ctrl info is NULL!\n");
        return -1;
    }

    *infoArray++ = ctrlInfo->u8MotorId;
    *infoArray++ = ctrlInfo->u8Direction;

    *infoArray++ = ctrlInfo->u16Speed & 0xFF;
    *infoArray++ = (ctrlInfo->u16Speed >> 8) & 0xFF;

    *infoArray++ = ctrlInfo->u16Step & 0xFF;
    *infoArray = (ctrlInfo->u16Step >> 8) & 0xFF;

    return 0;
}

int MotorCtrlInfoUnPack(struct MotorCtrlInfo* ctrlInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!infoArray)
    {
        printf("info Array is NULL!\n");
        return -1;
    }

    if (arrayLen != MotorCtrlInfoLength())
    {
        printf("array len is:%d, not equal motor ctrl info length:%d\n", arrayLen, MotorCtrlInfoLength());
        return -1;
    }

    if (!ctrlInfo)
    {
        printf("ctrl info is NULL!\n");
        return -1;
    }

    ctrlInfo->u8MotorId = infoArray[0];
    ctrlInfo->u8Direction = infoArray[1];
    ctrlInfo->u16Speed = (infoArray[3] << 8) | infoArray[2];
    ctrlInfo->u16Step = (infoArray[5] << 8) | infoArray[4];

    return 0;
}

int MotorPidInfoLength(void)
{
    return 13;
}

int MotorPidInfoPack(const struct MotorPidInfo* pidInfo, uint8_t* infoArray)
{
    if (!pidInfo)
    {
        printf("pid info is NULL!\n");
        return -1;
    }

    if (!infoArray)
    {
        printf("info array is NULL!\n");
        return -1;
    }

    infoArray[0] = pidInfo->u8MotorId;
    memcpy(infoArray + 1, &pidInfo->P, sizeof(float));
    memcpy(infoArray + 5, &pidInfo->I, sizeof(float));
    memcpy(infoArray + 9, &pidInfo->D, sizeof(float));

    return 0;
}

int MotorPidInfoUnPack(struct MotorPidInfo* pidInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!infoArray)
    {
        printf("info array is NULL!\n");
        return -1;
    }

    if (!pidInfo)
    {
        printf("pid info is NULL!\n");
        return -1;
    }

    if (arrayLen != MotorPidInfoLength())
    {
        printf("array len:%d is not equal pid info length:%d\n", arrayLen, MotorPidInfoLength());
        return -1;
    }

    pidInfo->u8MotorId = infoArray[0];
    memcpy(&pidInfo->P, infoArray + 1, sizeof(float));
    memcpy(&pidInfo->I, infoArray + 5, sizeof(float));
    memcpy(&pidInfo->D, infoArray + 9, sizeof(float));

    return 0;
}

static int AiResultRealLength()
{
    return 10;
}

int AiPacketLength(uint8_t aiResultCount)
{
    return AiResultRealLength() * aiResultCount + 1;
}

int AiPacketInfoInit(struct AiPacketInfo* info, uint8_t* array, uint8_t aiResultCount)
{
    if (!info)
    {
        printf("info is NULL!\n");
        return -1;
    }

    if (!array)
    {
        printf("array is NULL!\n");
        return -1;
    }

    info->infoArray = array;
    info->aiResultMaxCount = aiResultCount;
    info->curCount = 0;

    info->infoArray[0] = aiResultCount;

    return 0;
}

int AiPacketAddResult(struct AiPacketInfo* packet, const struct AiResult* result)
{
    uint8_t* array = NULL;

    if (!packet || !result)
    {
        printf("packet or result is NULL!\n");
        return -1;
    }

    if (packet->curCount >= packet->aiResultMaxCount)
    {
        printf("packet curCount:%d, maxCount:%d\n", packet->curCount, packet->aiResultMaxCount);
        return -1;
    }

    array = packet->infoArray + packet->curCount * AiResultRealLength() + 1;
    *array++ = result->u8Conf;
    *array++ = result->u8Id;
    *array++ = (uint8_t)(result->u16X0 & 0xFF);
    *array++ = (uint8_t)((result->u16X0 >> 8) & 0xFF) ;
    *array++ = (uint8_t)(result->u16Y0 & 0xFF);
    *array++ = (uint8_t)((result->u16Y0 >> 8) & 0xFF) ;
    *array++ = (uint8_t)(result->u16X1 & 0xFF);
    *array++ = (uint8_t)((result->u16X1 >> 8) & 0xFF) ;
    *array++ = (uint8_t)(result->u16Y1 & 0xFF);
    *array = (uint8_t)((result->u16Y1 >> 8) & 0xFF) ;

    packet->curCount += 1;

    return 0;
}

int AiResultIter(struct AiResultIterator* iter, uint8_t* packetArray)
{
    if (!iter || !packetArray)
    {
        printf("iter or packetArray is NULL!\n");
        return -1;
    }

    iter->u8PacketArray = packetArray;
    iter->resultCount = packetArray[0];
    iter->curResultIndex = 0;

    memset(&iter->curResult, 0, sizeof(struct AiResult));

    return 0;
}

struct AiResult* AiResultIterNext(struct AiResultIterator* iter)
{
    uint8_t* array = NULL;

    if (!iter)
    {
        printf("iter is NULL!\n");
        return NULL;
    }

    if (iter->resultCount == 0)
    {
        return NULL;
    }

    if (iter->curResultIndex >= iter->resultCount)
        return NULL;

    array = iter->u8PacketArray + iter->curResultIndex * AiResultRealLength() + 1;

    iter->curResult.u8Conf = array[0];
    iter->curResult.u8Id   = array[1];
    iter->curResult.u16X0 = ((uint16_t)array[3] << 8) | (uint16_t)array[2];
    iter->curResult.u16Y0 = ((uint16_t)array[5] << 8) | (uint16_t)array[4];
    iter->curResult.u16X1 = ((uint16_t)array[7] << 8) | (uint16_t)array[6];
    iter->curResult.u16Y1 = ((uint16_t)array[9] << 8) | (uint16_t)array[8];

    iter->curResultIndex += 1;

    return &iter->curResult;
}

int AiLogSplit(struct AiResult* inputAiResArr, uint8_t aiResCnt, struct LogHead* logHead, AiLogSplitCallback callback)
{
    if (!inputAiResArr || !logHead || !callback) {
        return -1;
    }

    uint8_t logPktNum = 0;
    uint8_t aiResHandledCnt = 0;
    uint8_t aiResRemainCnt = aiResCnt;
    uint8_t aiResCntInOnePkt = 0;
    uint8_t pktArr[MAX_LOG_PKT_LEN];
    uint8_t pktSize = 0;
    logHead->oriPktLength = sizeof(struct LogHead) + AiPacketLength(aiResCnt);
    struct timeval tv;

    while (aiResRemainCnt > 0) {
        memset(pktArr, 0, MAX_LOG_PKT_LEN);
        // Copy LogHead to the current packet
        logHead->pktIndex = logPktNum;
        gettimeofday(&tv, NULL);
        logHead->timeStamp = (unsigned long long)(tv.tv_sec - 100000) * 1000 + (unsigned long long)(tv.tv_usec) / 1000; /* 100000 is the offset of the time stamp */
        memcpy(pktArr, logHead, sizeof(struct LogHead));
        uint8_t* currentPktPtr = pktArr + sizeof(struct LogHead);  // Move the pointer after LogHead

        // Calculate the number of results in this packet
        aiResCntInOnePkt = aiResRemainCnt > MAX_AI_RES_NUM ? MAX_AI_RES_NUM : aiResRemainCnt;

        // Store the number of results in the first byte of the packet
        *currentPktPtr = aiResCntInOnePkt;
        currentPktPtr++;  // Move the pointer to the next byte

        // Copy the AiResult data to the packet
        memcpy(currentPktPtr, inputAiResArr + aiResHandledCnt, sizeof(struct AiResult) * aiResCntInOnePkt);
        currentPktPtr += sizeof(struct AiResult) * aiResCntInOnePkt;  // Move the pointer after the AiResult data

        aiResHandledCnt += aiResCntInOnePkt;
        aiResRemainCnt -= aiResCntInOnePkt;

        // Calculate the size of the current packet
        pktSize = sizeof(struct AiResult) * aiResCntInOnePkt + sizeof(struct LogHead) + 1;  // +1 for the count byte
        logPktNum++;  // Move to the next packet
        callback(pktArr, pktSize);
    }
    return 0;
}

int AiLogReassemblerInit(AiLogReassembler* reassembler, size_t aiResbufferSize, AiLogReassembleCallback callback)
{
    if (!reassembler) {
        printf("AiLogReassemblerInit failed!\n");
        return -1;
    }
    reassembler->aiResbufferSize = aiResbufferSize;
    reassembler->aiResbuffer = (uint8_t*)malloc(aiResbufferSize);
    if (!reassembler->aiResbuffer) {
        printf("AiLogReassemblerInit failed!\n");
        AiLogReassemblerDestroy(reassembler);
        return -1;
    }
    reassembler->logHead = (struct LogHead*)malloc(sizeof(struct LogHead));
    if (!reassembler->logHead) {
        printf("AiLogReassemblerInit failed!\n");
        AiLogReassemblerDestroy(reassembler);
        return -1;
    }
    reassembler->packetCache = NULL;
    reassembler->subPktNum = 1;
    reassembler->state = -1;
    reassembler->callback = callback;
    reassembler->lossPktNum = 0;
    return 0;
}

void AiLogReassemblerClear(AiLogReassembler *reassembler)
{
    if (!reassembler) {
        return;
    }
    if (reassembler->packetCache) {
        for (int i = 0; i < reassembler->subPktNum; ++i) {
            if (reassembler->packetCache[i]) {
                free(reassembler->packetCache[i]);
                reassembler->packetCache[i] = NULL;
            }
        }
        free(reassembler->packetCache);
        reassembler->packetCache = NULL;
    }
    memset(reassembler->aiResbuffer, 0, reassembler->aiResbufferSize);
    memset(reassembler->logHead, 0, sizeof(struct LogHead));
    reassembler->subPktNum = 1;
    reassembler->state = -1;
    reassembler->aiResHandledCnt = 0;
}

int AiLogReassemble(AiLogReassembler *reassembler, uint8_t* inputBuffer, uint32_t inputLen) {
    if (!reassembler || !inputBuffer) {
        printf("Unpack AI log, NULL ptr!\n");
        return -1;
    }
    if (inputLen < sizeof(struct LogHead) + 1) {
        AiLogReassemblerClear(reassembler);
        printf("wrong inputLen\n");
        return -1;
    }

    int bufferOffset = 0;
    struct LogHead logHead = {0};

    // 1. 解析 LogHead
    memcpy(&logHead, inputBuffer, sizeof(struct LogHead));
    bufferOffset += sizeof(struct LogHead);

    // 打印 LogHead 的内容用于调试
    printf("LogHead - Length: %u, Yaw: %u, Pitch: %u, Packet Index: %u, Timestamp: %lld\n",
           logHead.oriPktLength, logHead.yawAngle, logHead.pitchAngle, logHead.pktIndex, logHead.timeStamp);

    if (reassembler->state == -1) {
        memset(reassembler->aiResbuffer, 0, reassembler->aiResbufferSize);
        memcpy(reassembler->logHead, &logHead, sizeof(struct LogHead));
        reassembler->subPktNum = 1 + (logHead.oriPktLength - sizeof(struct LogHead) - 2) / (MAX_AI_RES_NUM * sizeof(struct AiResult));
        reassembler->packetCache = (uint8_t**)malloc(reassembler->subPktNum * sizeof(uint8_t*));
        if (!reassembler->packetCache) {
            printf("create packet cache fail!\n");
            AiLogReassemblerClear(reassembler);
            return -1;
        }
        memset(reassembler->packetCache, 0, reassembler->subPktNum * sizeof(uint8_t*));
        reassembler->aiResHandledCnt = 0;
        reassembler->state = 0;
    }

    /* 2. 检查是否同属于一个重组包，如果不是，则说明丢包了，重置状态并记录丢包个数 */
    if ((logHead.pktIndex < reassembler->state) || (reassembler->packetCache[logHead.pktIndex])
        || (logHead.oriPktLength != reassembler->logHead->oriPktLength)
        || (logHead.pitchAngle != reassembler->logHead->pitchAngle)
        || (logHead.yawAngle != reassembler->logHead->yawAngle)) {
        reassembler->lossPktNum += reassembler->subPktNum;
        AiLogReassemblerClear(reassembler);
        memcpy(reassembler->logHead, &logHead, sizeof(struct LogHead));
        reassembler->subPktNum = 1 + (logHead.oriPktLength - sizeof(struct LogHead) - 2) / (MAX_AI_RES_NUM * sizeof(struct AiResult));
        reassembler->state = 0;
        reassembler->packetCache = (uint8_t**)malloc(reassembler->subPktNum * sizeof(uint8_t*));
        memset(reassembler->packetCache, 0, reassembler->subPktNum * sizeof(uint8_t*));
        printf("Detect lose %u packets!\n", reassembler->lossPktNum);
    }

    // 3. 获取当前数据包中AI结果的数量
    uint8_t aiResCntInOnePkt = inputBuffer[bufferOffset];  // 第一个字节存储了当前包的 AI 结果数量
    bufferOffset++;  // Move to the next byte

    if (inputLen < bufferOffset + sizeof(struct AiResult) * aiResCntInOnePkt) {
        printf("wrong inputLen\n");
        return -1;
    }

    if (logHead.pktIndex == reassembler->state) {
        // 4. 解析当前包中的 AiResult 数据
        memcpy(reassembler->aiResbuffer + reassembler->aiResHandledCnt, inputBuffer + bufferOffset, sizeof(struct AiResult) * aiResCntInOnePkt);
        reassembler->state++;
        reassembler->aiResHandledCnt += aiResCntInOnePkt;
        // 尝试处理缓存中的包
        while (reassembler->state < reassembler->subPktNum && reassembler->packetCache[reassembler->state]) {
            uint8_t* cachedPacket = reassembler->packetCache[reassembler->state];
            uint8_t cachedAiResCnt = cachedPacket[0];
            memcpy(reassembler->aiResbuffer + reassembler->aiResHandledCnt, cachedPacket + 1, sizeof(struct AiResult) * cachedAiResCnt);
            reassembler->aiResHandledCnt += cachedAiResCnt;
            // 释放缓存
            free(cachedPacket);
            reassembler->packetCache[reassembler->state] = NULL;
            reassembler->state++;
        }
    } else {
        // 5. 如果当前包是乱序包，缓存起来
        uint8_t* cachedPacket = (uint8_t*)malloc(1 + sizeof(struct AiResult) * aiResCntInOnePkt);
        if (!cachedPacket) {
            printf("Failed to allocate memory for cached packet!\n");
            AiLogReassemblerClear(reassembler);
            return -1;
        }
        cachedPacket[0] = aiResCntInOnePkt;
        memcpy(cachedPacket + 1, inputBuffer + bufferOffset, sizeof(struct AiResult) * aiResCntInOnePkt);
        reassembler->packetCache[logHead.pktIndex] = cachedPacket;
    }

    // 6. 判断是否已经完成一个包的重组
    if (reassembler->state == reassembler->subPktNum) {
        if (reassembler->callback != NULL) {
            reassembler->callback(reassembler->aiResbuffer, reassembler->aiResHandledCnt, reassembler->logHead);
        }
        AiLogReassemblerClear(reassembler);
    }
    return 0;
}

void AiLogReassemblerDestroy(AiLogReassembler *reassembler)
{
    if (reassembler == NULL) {
        return;
    }
    AiLogReassemblerClear(reassembler);
    if (reassembler->packetCache) {
        free(reassembler->packetCache);
        reassembler->packetCache = NULL;
    }
    if (reassembler->logHead) {
        free(reassembler->logHead);
        reassembler->logHead = NULL;
    }
    if (reassembler->aiResbuffer) {
        free(reassembler->aiResbuffer);
        reassembler->aiResbuffer = NULL;
    }
}

int AiSwitchInfoLength(void)
{
    return sizeof(struct AiSwitch);
}

int AiSwitchInfoPack(const struct AiSwitch* switchInfo, uint8_t* infoArray)
{
    if (!switchInfo || !infoArray)
    {
        printf("switch info or info array is NULL!\n");
        return -1;
    }

    infoArray[0] = switchInfo->u8AiSwitch;

    return 0;
}

int AiSwitchInfoUnPack(struct AiSwitch* switchInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!switchInfo)
    {
        printf("switch info is nULL!\n");
        return -1;
    }

    if (!infoArray)
    {
        printf("info array is NULL!\n");
        return -1;
    }

    if (arrayLen != AiSwitchInfoLength())
    {
        printf("array len:%d, is not equal switch info length:%d\n", arrayLen, AiSwitchInfoLength());
        return -1;
    }

    switchInfo->u8AiSwitch = infoArray[0];

    return 0;
}

int TrackingSwitchInfoLength(void)
{
    return sizeof(struct TrackingSwitch);
}

int TrackingSwitchInfoPack(const struct TrackingSwitch* switchInfo, uint8_t* infoArray)
{
    if (!switchInfo || !infoArray)
    {
        printf("switchInfo or infoArray is NULL!\n");
        return -1;
    }

    infoArray[0] = switchInfo->u8TrackingSwitch;

    return 0;
}

int TrackingSwitchInfoUnPack(struct TrackingSwitch* switchInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!switchInfo)
    {
        printf("switch info is nULL!\n");
        return -1;
    }

    if (!infoArray)
    {
        printf("info array is NULL!\n");
        return -1;
    }

    if (arrayLen != TrackingSwitchInfoLength())
    {
        printf("array len:%d, is not equal switch info length:%d\n", arrayLen, TrackingSwitchInfoLength());
        return -1;
    }

    switchInfo->u8TrackingSwitch = infoArray[0];

    return 0;
}

int MotorDecisionSwitchInfoLength(void)
{
    return sizeof(struct MotorDecisionSwitch);
}

int MotorDecisionSwitchInfoPack(const struct MotorDecisionSwitch* switchInfo, uint8_t* infoArray)
{
    if (!switchInfo || !infoArray)
    {
        printf("switchInfo or infoArray is NULL!\n");
        return -1;
    }

    infoArray[0] = switchInfo->u8DecisionSwitch;

    return 0;
}

int MotorDecisionSwitchInfoUnPack(struct MotorDecisionSwitch* switchInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!switchInfo || !infoArray)
    {
        printf("switch info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen != MotorDecisionSwitchInfoLength())
    {
        printf("array len:%d, is not equal decision switch info length:%d\n", arrayLen, MotorDecisionSwitchInfoLength());
        return -1;
    }

    switchInfo->u8DecisionSwitch = infoArray[0];

    return 0;
}

int SoftPowerOffSwitchInfoLength(void)
{
    return sizeof(struct SoftPowerOffSwitch);
}

int SoftPowerOffSwitchInfoPack(const struct SoftPowerOffSwitch* switchInfo, uint8_t* infoArray)
{
    if (!switchInfo || !infoArray)
    {
        printf("switch info or info array is NULL!\n");
        return -1;
    }

    infoArray[0] = switchInfo->u8SoftPowerOffSwitch;

    return 0;
}

int SoftPowerOffSwitchInfoUnPack(struct SoftPowerOffSwitch* switchInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!switchInfo || !infoArray)
    {
        printf("switch info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen != SoftPowerOffSwitchInfoLength())
    {
        printf("array len:%d, is not equal soft power off switch info length:%d\n", arrayLen, SoftPowerOffSwitchInfoLength());
        return -1;
    }

    switchInfo->u8SoftPowerOffSwitch = infoArray[0];

    return 0;
}

int BluetoothPairingStatusLength(void)
{
    return 1;
}

int BluetoothPairingStatusPack(const struct BluetoothPairingStatus* statusInfo, uint8_t* infoArray)
{
    if (!statusInfo || !infoArray)
    {
        printf("status info or info array is NULL!\n");
        return -1;
    }

    infoArray[0] = statusInfo->u8Status;

    return 0;
}

int BluetoothPairingStatusUnPack(struct BluetoothPairingStatus* statusInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!statusInfo || !infoArray)
    {
        printf("status info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen != BluetoothPairingStatusLength())
    {
        printf("array len:%d, is not equal bluetooth pairing status length:%d\n", arrayLen, BluetoothPairingStatusLength());
        return -1;
    }

    statusInfo->u8Status = infoArray[0];

    return 0;
}

int BluetoothUpgradeLength(struct BluetoothUpgrade* upgrade)
{
    if (!upgrade)
    {
        printf("upgrade is NULL!\n");
        return 0;
    }

    return (int)(strlen((const char*)upgrade->u8FirmwarePath) + 1);
}

int BluetoothUpgradePack(const struct BluetoothUpgrade* upgradeInfo, uint8_t* infoArray)
{
    if (!upgradeInfo || !infoArray)
    {
        printf("upgrade info or info array is NULL!\n");
        return -1;
    }

    strcpy((char*)infoArray, (const char*)upgradeInfo->u8FirmwarePath);

    return 0;
}

int BluetoothUpgradeUnPack(struct BluetoothUpgrade* upgradeInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!upgradeInfo || !infoArray)
    {
        printf("upgrade info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen >= (sizeof(upgradeInfo->u8FirmwarePath) - 1))
    {
        printf("array len:%d, is bigger than firmware path max size:%zd\n", arrayLen, sizeof(upgradeInfo->u8FirmwarePath));
        return -1;
    }

    memset(upgradeInfo, 0, sizeof(struct BluetoothUpgrade));

    strncpy((char*)upgradeInfo->u8FirmwarePath, (const char*)infoArray, arrayLen);

    return 0;
}

int BatteryLevelLength(void)
{
    return sizeof(struct BatteryLevel);
}

int BatteryLevelPack(const struct BatteryLevel* levelInfo, uint8_t* infoArray)
{
    if (!levelInfo || !infoArray)
    {
        printf("battery level info or info array is NULL!\n");
        return -1;
    }

    *infoArray++ = levelInfo->u16BatteryLevel & 0xFF;
    *infoArray = (levelInfo->u16BatteryLevel >> 8) & 0xFF;

    return 0;
}

int BatteryLevelUnPack(struct BatteryLevel* levelInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!levelInfo || !infoArray)
    {
        printf("level info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen != BatteryLevelLength())
    {
        printf("array len:%d, is not equal battery level length:%d\n", arrayLen, BatteryLevelLength());
        return -1;
    }

    levelInfo->u16BatteryLevel = (infoArray[1] << 8) | infoArray[0];

    return 0;
}

int SideLightInfoLength(void)
{
    return sizeof(struct SideLightInfo);
}

int SideLightInfoPack(const struct SideLightInfo* lightInfo, uint8_t* infoArray)
{
    if (!lightInfo || !infoArray)
    {
        printf("side light info or info array is NULL!\n");
        return -1;
    }

    infoArray[0] = lightInfo->u8SideLightInfo;

    return 0;
}

int SideLightInfoUnPack(struct SideLightInfo* lightInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!lightInfo || !infoArray)
    {
        printf("light info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen != SideLightInfoLength())
    {
        printf("array len:%d, is not equal side light info length:%d\n", arrayLen, SideLightInfoLength());
        return -1;
    }

    lightInfo->u8SideLightInfo = infoArray[0];

    return 0;
}

int BluetoothNameInfoLength(void)
{
    return sizeof(struct BluetoothNameInfo);
}

int BluetoothNameInfoPack(const struct BluetoothNameInfo* nameInfo, uint8_t* infoArray)
{
    if (!nameInfo || !infoArray)
    {
        printf("bluetooth name info or info array is NULL!\n");
        return -1;
    }

    strncpy((char*)infoArray, (const char*)nameInfo->u8Name, sizeof(struct BluetoothNameInfo));

    return 0;
}

int BluetoothNameInfoUnPack(struct BluetoothNameInfo* nameInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!nameInfo || !infoArray)
    {
        printf("name info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen != BluetoothNameInfoLength())
    {
        printf("array len:%d, is not equal bluetooth name info length:%d\n", arrayLen, BluetoothNameInfoLength());
        return -1;
    }

    memset(nameInfo, 0, sizeof(struct BluetoothNameInfo));

    strncpy((char*)nameInfo->u8Name, (const char*)infoArray, arrayLen);

    return 0;
}

int MotorPositionLength(void)
{
    return 5;
}

int MotorPositionPack(const struct MotorPosition* positionInfo, uint8_t* infoArray)
{
    if (!positionInfo || !infoArray)
    {
        printf("info array or position info is NULL!\n");
        return -1;
    }

    *infoArray++ = positionInfo->u8MotorId;

    *infoArray++ = positionInfo->i32Position & 0xFF;
    *infoArray++ = (positionInfo->i32Position >> 8) & 0xFF;
    *infoArray++ = (positionInfo->i32Position >> 16) & 0xFF;
    *infoArray = (positionInfo->i32Position >> 24) & 0xFF;

    return 0;
}

int MotorPositionUnPack(struct MotorPosition* positionInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!positionInfo || !infoArray)
    {
        printf("info Array or position info is NULL!\n");
        return -1;
    }

    if (arrayLen != MotorPositionLength())
    {
        printf("array len is:%d, not equal motor position length:%d\n", arrayLen, MotorPositionLength());
        return -1;
    }

    positionInfo->u8MotorId = infoArray[0];
    positionInfo->i32Position = (infoArray[4] << 24) | (infoArray[3] << 16) | (infoArray[2] << 8) | infoArray[1];

    return 0;
}

int BluetoothHiddenSwitchLength(void)
{
    return 1;
}

int BluetoothHiddenSwitchPack(const struct BluetoothHiddenSwitch* hiddenSwitch, uint8_t* infoArray)
{
    if (!hiddenSwitch || !infoArray)
    {
        printf("info array or hidden switch is NULL!\n");
        return -1;
    }

    infoArray[0] = hiddenSwitch->u8Hidden;

    return 0;
}

int BluetoothHiddenSwitchUnPack(struct BluetoothHiddenSwitch* hiddenSwitch, const uint8_t* infoArray, int arrayLen)
{
    if (!hiddenSwitch || !infoArray)
    {
        printf("info array or hidden switch is NULL!\n");
        return -1;
    }

    if (arrayLen != BluetoothHiddenSwitchLength())
    {
        printf("array len is:%d, not equal bluetooth hidden siwtch length:%d\n", arrayLen, BluetoothHiddenSwitchLength());
        return -1;
    }

    hiddenSwitch->u8Hidden = infoArray[0];

    return 0;
}

int ChargeDetectInfoLength(void)
{
    return 1;
}

int ChargeDetectInfoPack(const struct ChargeDetectInfo* chargeInfo, uint8_t* infoArray)
{
    if (!chargeInfo || !infoArray)
    {
        printf("info array or charge detect info is NULL!\n");
        return -1;
    }

    infoArray[0] = chargeInfo->u8ChargeInfo;

    return 0;
}

int ChargeDetectInfoUnPack(struct ChargeDetectInfo* chargeInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!chargeInfo || !infoArray)
    {
        printf("info array or charge detect info is NULL!\n");
        return -1;
    }

    if (arrayLen != ChargeDetectInfoLength())
    {
        printf("array len is:%d, not equal charge detect info length:%d\n", arrayLen, ChargeDetectInfoLength());
        return -1;
    }

    chargeInfo->u8ChargeInfo = infoArray[0];

    return 0;
}

int TransferRequestLength()
{
    return 73 + 1;
}

int TransferRequestPack(const struct TransferRequest* requestInfo, uint8_t* infoArray)
{
    int nameLength = 0;
    uint8_t* infoArrayPtr = NULL;

    if (!requestInfo || !infoArray)
    {
        printf("info array or request info is NULL!\n");
        return -1;
    }

    nameLength = sizeof(requestInfo->u8TransferFileName);

    infoArray[0] = 0;

    memcpy(infoArray + 1, requestInfo->u8TransferFileName, nameLength);

    infoArrayPtr = infoArray + nameLength + 1;

    *infoArrayPtr++ = (uint8_t)(requestInfo->u32TransferTotalLength & 0xFF);
    *infoArrayPtr++ = (uint8_t)((requestInfo->u32TransferTotalLength >> 8) & 0xFF);
    *infoArrayPtr++ = (uint8_t)((requestInfo->u32TransferTotalLength >> 16) & 0xFF);
    *infoArrayPtr++ = (uint8_t)((requestInfo->u32TransferTotalLength >> 24) & 0xFF);
    *infoArrayPtr++ = (uint8_t)(requestInfo->u32TotalPacketCnt & 0xFF);
    *infoArrayPtr++ = (uint8_t)((requestInfo->u32TotalPacketCnt >> 8 ) & 0xFF);
    *infoArrayPtr++ = (uint8_t)((requestInfo->u32TotalPacketCnt >> 16) & 0xFF);
    *infoArrayPtr++ = (uint8_t)((requestInfo->u32TotalPacketCnt >> 24) & 0xFF);
    *infoArrayPtr = requestInfo->u8PacketSize;

    return 0;
}

int TransferRequestUnPack(struct TransferRequest* requestInfo, const uint8_t* infoArray, int arrayLen)
{
    int nameLength = 0;
    uint8_t* infoArrayPtr = NULL;

    if (!requestInfo || !infoArray)
    {
        printf("info array or request info is NULL!\n");
        return -1;
    }

    if (arrayLen != TransferRequestLength())
    {
        printf("array len is:%d, not equal request info length:%d\n", arrayLen, TransferRequestLength());
        return -1;
    }

    if (infoArray[0] != 0)
    {
        printf("infoArray cmd is not 0 for transfer request, illegal!\n");
        return -1;
    }

    nameLength = sizeof(requestInfo->u8TransferFileName);

    memcpy(requestInfo->u8TransferFileName, infoArray + 1, nameLength);

    infoArrayPtr = (uint8_t*)(infoArray + nameLength + 1);

    requestInfo->u32TransferTotalLength = (uint32_t)(((uint32_t)infoArrayPtr[3] << 24) | ((uint32_t)infoArrayPtr[2] << 16) | ((uint32_t)infoArrayPtr[1] << 8) | (uint32_t)infoArrayPtr[0]);
    requestInfo->u32TotalPacketCnt = (uint32_t)(((uint32_t)infoArrayPtr[7] << 24) | ((uint32_t)infoArrayPtr[6] << 16) | ((uint32_t)infoArrayPtr[5] << 8) | (uint32_t)infoArrayPtr[4]);
    requestInfo->u8PacketSize = infoArrayPtr[8];


    return 0;
}

int TransferRequestDataLength()
{
    return 5;
}

int TransferRequestDataPack(const struct TransferRequestData* requestDataInfo, uint8_t* infoArray)
{
    if (!requestDataInfo || !infoArray)
    {
        printf("info array or request data info is NULL!\n");
        return -1;
    }
    *infoArray++ = 2;

    *infoArray++ = (uint8_t)(requestDataInfo->u32Id & 0xFF);
    *infoArray++ = (uint8_t)((requestDataInfo->u32Id >> 8) & 0xFF);
    *infoArray++ = (uint8_t)((requestDataInfo->u32Id >> 16) & 0xFF);
    *infoArray++ = (uint8_t)((requestDataInfo->u32Id >> 24) & 0xFF);

    return 0;
}

int TransferRequestDataUnPack(struct TransferRequestData* requestDataInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!requestDataInfo || !infoArray)
    {
        printf("info array or request data info is NULL!\n");
        return -1;
    }

    if (arrayLen != TransferRequestDataLength())
    {
        printf("array len is:%d, not equal request data info length:%d\n", arrayLen, TransferRequestDataLength());
        return -1;
    }

    if (infoArray[0] != 2)
    {
        printf("infoArray cmd is not 2  for transfer request data, illegal!\n");
        return -1;
    }

    requestDataInfo->u32Id = (infoArray[4] << 24) | (infoArray[3] << 16) | (infoArray[2] << 8) | infoArray[1];

    return 0;
}

int TransferDataLength(uint8_t u8DataLength)
{
    return u8DataLength + 5 + 1;
}

int TransferDataPack(const struct TransferData* dataInfo, uint8_t* infoArray)
{
    if (!dataInfo || !infoArray)
    {
        printf("info array or data info is NULL!\n");
        return -1;
    }

    *infoArray++ = 3;

    *infoArray++ = dataInfo->u32Id & 0xFF;
    *infoArray++ = (dataInfo->u32Id >> 8) & 0xFF;
    *infoArray++ = (dataInfo->u32Id >> 16) & 0xFF;
    *infoArray++ = (dataInfo->u32Id >> 24) & 0xFF;

    *infoArray++ = dataInfo->u8DataSize;

    memcpy(infoArray, dataInfo->data, dataInfo->u8DataSize);

    return 0;
}

int TransferDataUnPack(struct TransferData* dataInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!dataInfo || !infoArray)
    {
        printf("info array or data info is NULL!\n");
        return -1;
    }

    if (infoArray[0] != 3)
    {
        printf("infoArray cmd is not 3 for transfer data! illegal!\n");
        return -1;
    }

    dataInfo->u32Id = ((uint32_t)infoArray[4] << 24) | ((uint32_t)infoArray[3] << 16) | ((uint32_t)infoArray[2] << 8) | infoArray[1];

    dataInfo->u8DataSize = infoArray[5];

    if (TransferDataLength(dataInfo->u8DataSize) != arrayLen)
    {
        printf("info array length:%d is not equal data info length:%d\n", arrayLen, TransferDataLength(dataInfo->u8DataSize));
        return -1;
    }

    memcpy(dataInfo->data, infoArray + 5 + 1, dataInfo->u8DataSize);

    return 0;
}

int TemperatureInfoLength(void)
{
    return 1;
}

int TemperatureInfoPack(const struct TemperatureInfo* tempInfo, uint8_t* infoArray)
{
    if (!tempInfo || !infoArray)
    {
        printf("info array or temp info is NULL!\n");
        return -1;
    }

    infoArray[0] = tempInfo->u8Temperature;

    return 0;
}

int TemperatureInfoUnPack(struct TemperatureInfo* tempInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!tempInfo || !infoArray)
    {
        printf("info array or temperature info is NULL!\n");
        return -1;
    }

    if (arrayLen != TemperatureInfoLength())
    {
        printf("array len is:%d, not equal temperature info length:%d\n", arrayLen, TemperatureInfoLength());
        return -1;
    }

    tempInfo->u8Temperature = infoArray[0];

    return 0;
}

int DebugCtrlInfoLength(void)
{
    return 9;
}

int DebugCtrlInfoPack(const struct DebugCtrlInfo* debugInfo, uint8_t* infoArray)
{
    if (!debugInfo || !infoArray)
    {
        printf("info array or debug info is NULL!\n");
        return -1;
    }

    *infoArray++ = debugInfo->u8Cmd;

    *infoArray++ = (uint8_t)(debugInfo->i64TimeStamp & 0xFF);
    *infoArray++ = (uint8_t)((debugInfo->i64TimeStamp >> 8) & 0xFF);
    *infoArray++ = (uint8_t)((debugInfo->i64TimeStamp >> 16) & 0xFF);
    *infoArray++ = (uint8_t)((debugInfo->i64TimeStamp >> 24) & 0xFF);
    *infoArray++ = (uint8_t)((debugInfo->i64TimeStamp >> 32) & 0xFF);
    *infoArray++ = (uint8_t)((debugInfo->i64TimeStamp >> 40) & 0xFF);
    *infoArray++ = (uint8_t)((debugInfo->i64TimeStamp >> 48) & 0xFF);
    *infoArray = (uint8_t)((debugInfo->i64TimeStamp >> 56) & 0xFF);

    return 0;
}

int DebugCtrlInfoUnPack(struct DebugCtrlInfo* debugInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!debugInfo || !infoArray)
    {
        printf("info array or debug info is NULL!\n");
        return -1;
    }

    if (arrayLen != DebugCtrlInfoLength())
    {
        printf("array len is:%d, not equal debug ctrl info length:%d\n", arrayLen, DebugCtrlInfoLength());
        return -1;
    }

    debugInfo->u8Cmd = infoArray[0];

    debugInfo->i64TimeStamp = (int64_t)(((int64_t)infoArray[8] << 56) | ((int64_t)infoArray[7] << 48) | ((int64_t)infoArray[6] << 40) | ((int64_t)infoArray[5] << 32)
                                        | ((int64_t)infoArray[4] << 24) | ((int64_t)infoArray[3] << 16) | ((int64_t)infoArray[2] << 8) | ((int64_t)infoArray[1]));

    return 0;
}

int FirmwareUpdateInfoLength(void)
{
    return sizeof(struct FirmwareUpdateInfo);
}

int FirmwareUpdateInfoPack(const struct FirmwareUpdateInfo* updateInfo, uint8_t* infoArray)
{
    if (!updateInfo || !infoArray)
    {
        printf("firmware update info or info array is NULL!\n");
        return -1;
    }

    strncpy((char*)infoArray, (const char*)updateInfo->u8FirmwareName, sizeof(struct FirmwareUpdateInfo));

    return 0;
}

int FirmwareUpdateInfoUnPack(struct FirmwareUpdateInfo* updateInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!updateInfo || !infoArray)
    {
        printf("firmware update info or info array is NULL!\n");
        return -1;
    }

    if (arrayLen != FirmwareUpdateInfoLength())
    {
        printf("array len:%d, is not equal firmware update info length:%d\n", arrayLen, FirmwareUpdateInfoLength());
        return -1;
    }

    memset(updateInfo, 0, sizeof(struct BluetoothNameInfo));

    strncpy((char*)updateInfo->u8FirmwareName, (const char*)infoArray, arrayLen);

    return 0;
}

int FirmwareUpdateStatusLength(void)
{
    return 1;
}

int FirmwareUpdateStatusPack(const struct FirmwareUpdateStatus* updateStaus, uint8_t* infoArray)
{
    if (!updateStaus || !infoArray)
    {
        printf("info array or firmware update status is NULL!\n");
        return -1;
    }

    infoArray[0] = updateStaus->u8UpdateStatus;

    return 0;
}

int FirmwareUpdateStatusUnPack(struct FirmwareUpdateStatus* updateStatus, const uint8_t* infoArray, int arrayLen)
{
    if (!updateStatus || !infoArray)
    {
        printf("info array or update status is NULL!\n");
        return -1;
    }

    if (arrayLen != FirmwareUpdateStatusLength())
    {
        printf("array len is:%d, not equal firmware update status length:%d\n", arrayLen, FirmwareUpdateStatusLength());
        return -1;
    }

    updateStatus->u8UpdateStatus = infoArray[0];

    return 0;
}

int BluetoothVersionInfoLength(void)
{
    return 2;
}

int BluetoothVersionInfoPack(const struct BluetoothVersionInfo* versionInfo, uint8_t* infoArray)
{
    if (!versionInfo || !infoArray)
    {
        printf("info array or version info is NULL!\n");
        return -1;
    }

    *infoArray++ = versionInfo->u8MajorId;
    *infoArray   = versionInfo->u8MinorId;

    return 0;
}

int BluetoothVersionInfoUnPack(struct BluetoothVersionInfo* versionInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!versionInfo || !infoArray)
    {
        printf("info array or version info is NULL!\n");
        return -1;
    }

    if (arrayLen != BluetoothVersionInfoLength())
    {
        printf("array len is:%d, not equal bluetooth version info length:%d\n", arrayLen, BluetoothVersionInfoLength());
        return -1;
    }

    versionInfo->u8MajorId = infoArray[0];
    versionInfo->u8MinorId = infoArray[1];

    return 0;
}

int FirmwareVersionInfoLength(void)
{
    return 10;
}

int FirmwareVersionInfoPack(const struct FirmwareVersionInfo* versionInfo, uint8_t* infoArray)
{
    if (!versionInfo || !infoArray)
    {
        printf("info array or version info is NULL!\n");
        return -1;
    }

    *infoArray++ = versionInfo->u16BluetoothMajorId & 0xFF;
    *infoArray++ = (versionInfo->u16BluetoothMajorId >> 8) & 0xFF;
    *infoArray++ = versionInfo->u16BluetoothMinorId & 0xFF;
    *infoArray++ = (versionInfo->u16BluetoothMinorId >> 8) & 0xFF;
    *infoArray++ = versionInfo->u16FirmwareHardwareId & 0xFF;
    *infoArray++ = (versionInfo->u16FirmwareHardwareId >> 8) & 0xFF;
    *infoArray++ = versionInfo->u16FirmwareSystemId & 0xFF;
    *infoArray++ = (versionInfo->u16FirmwareSystemId >> 8) & 0xFF;
    *infoArray++ = versionInfo->u16FirmwareAppId & 0xFF;
    *infoArray   = (versionInfo->u16FirmwareAppId >> 8) & 0xFF;

    return 0;
}

int FirmwareVersionInfoUnPack(struct FirmwareVersionInfo* versionInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!versionInfo || !infoArray)
    {
        printf("info array or version info is NULL!\n");
        return -1;
    }

    if (arrayLen != FirmwareVersionInfoLength())
    {
        printf("array len is:%d, not equal firmware version info length:%d\n", arrayLen, FirmwareVersionInfoLength());
        return -1;
    }

    versionInfo->u16BluetoothMajorId = (infoArray[1] << 8) | infoArray[0];
    versionInfo->u16BluetoothMinorId = (infoArray[3] << 8) | infoArray[2];
    versionInfo->u16FirmwareHardwareId = (infoArray[5] << 8) | infoArray[4];
    versionInfo->u16FirmwareSystemId = (infoArray[7] << 8) | infoArray[6];
    versionInfo->u16FirmwareAppId = (infoArray[9] << 8) | infoArray[8];

    return 0;
}

int FirmwareVersionRequestLength(void)
{
    return 1;
}

int FirmwareVersionRequestPack(const struct FirmwareVersionRequest* request, uint8_t* infoArray)
{
    if (!request || !infoArray)
    {
        printf("info array or version request info is NULL!\n");
        return -1;
    }

    *infoArray = request->u8Request;

    return 0;
}

int FirmwareVersionRequestUnPack(struct FirmwareVersionRequest* request, const uint8_t* infoArray, int arrayLen)
{
    if (!request || !infoArray)
    {
        printf("info array or version request info is NULL!\n");
        return -1;
    }

    if (arrayLen != FirmwareVersionRequestLength())
    {
        printf("array len is:%d, not equal firmware version info length:%d\n", arrayLen, FirmwareVersionRequestLength());
        return -1;
    }

    request->u8Request = *infoArray;

    return 0;
}

int ResetBluetoothLength(void)
{
    return 1;
}

int ResetBluetoothPack(const struct ResetBluetooth* resetInfo, uint8_t* infoArray)
{
    if (!resetInfo || !infoArray)
    {
        printf("info array or bluetooth reset info is NULL!\n");
        return -1;
    }

    *infoArray = resetInfo->u8Cmd;

    return 0;
}

int ResetBluetoothUnPack(struct ResetBluetooth* resetInfo, const uint8_t* infoArray, int arrayLen)
{
    if (!resetInfo || !infoArray)
    {
        printf("info array or bluetooth reset info is NULL!\n");
        return -1;
    }

    if (arrayLen != ResetBluetoothLength())
    {
        printf("array len is:%d, not equal bluetooth reset info length:%d\n", arrayLen, ResetBluetoothLength());
        return -1;
    }

    resetInfo->u8Cmd = *infoArray;

    return 0;
}

int OtgChargeCfgLength(void)
{
    return 1;
}

int OtgChargeCfgPack(const struct OtgChargeCfg* cfg, uint8_t* infoArray)
{
    if (!cfg || !infoArray)
    {
        printf("info array or cfg info is NULL!\n");
        return -1;
    }

    *infoArray = cfg->u8Cfg;

    return 0;
}

int OtgChargeCfgUnPack(struct OtgChargeCfg* cfg, const uint8_t* infoArray, int arrayLen)
{
    if (!cfg || !infoArray)
    {
        printf("info array or cfg otg charge info is NULL!\n");
        return -1;
    }

    if (arrayLen != OtgChargeCfgLength())
    {
        printf("array len is:%d, not equal otg charge cfg info length:%d\n", arrayLen, OtgChargeCfgLength());
        return -1;
    }

    cfg->u8Cfg = infoArray[0];

    return 0;
}

int DetectRoiCfgLength(void)
{
    return 17;
}

int DetectRoiCfgPack(const struct DetectRoiCfg* cfg, uint8_t* infoArray)
{
    if (!cfg || !infoArray)
    {
        printf("info array or roi cfg info is NULL!\n");
        return -1;
    }

    *infoArray++ = cfg->u8RoiNum;

    *infoArray++ = cfg->u16X0 & 0xFF;
    *infoArray++ = (cfg->u16X0 >> 8) & 0xFF;

    *infoArray++ = cfg->u16Y0 & 0xFF;
    *infoArray++ = (cfg->u16Y0 >> 8) & 0xFF;

    *infoArray++ = cfg->u16Width0 & 0xFF;
    *infoArray++ = (cfg->u16Width0 >> 8) & 0xFF;

    *infoArray++ = cfg->u16Height0 & 0xFF;
    *infoArray++ = (cfg->u16Height0 >> 8) & 0xFF;

    *infoArray++ = cfg->u16X1 & 0xFF;
    *infoArray++ = (cfg->u16X1 >> 8) & 0xFF;

    *infoArray++ = cfg->u16Y1 & 0xFF;
    *infoArray++ = (cfg->u16Y1 >> 8) & 0xFF;

    *infoArray++ = cfg->u16Width1 & 0xFF;
    *infoArray++ = (cfg->u16Width1 >> 8) & 0xFF;

    *infoArray++ = cfg->u16Height1 & 0xFF;
    *infoArray++ = (cfg->u16Height1 >> 8) & 0xFF;

    return 0;
}

int DetectRoiCfgUnPack(struct DetectRoiCfg* cfg, const uint8_t* infoArray, int arrayLen)
{
    if (!cfg || !infoArray)
    {
        printf("info array or roi cfg info is NULL!\n");
        return -1;
    }

    if (arrayLen != DetectRoiCfgLength())
    {
        printf("array len is:%d, not equal roi cfg info length:%d\n", arrayLen, DetectRoiCfgLength());
        return -1;
    }

    cfg->u8RoiNum = infoArray[0];

    cfg->u16X0 = (infoArray[2] << 8) | infoArray[1];
    cfg->u16Y0 = (infoArray[4] << 8) | infoArray[3];
    cfg->u16Width0 = (infoArray[6] << 8) | infoArray[5];
    cfg->u16Height0 = (infoArray[8] << 8) | infoArray[7];

    cfg->u16X1 = (infoArray[10] << 8) | infoArray[9];
    cfg->u16Y1 = (infoArray[12] << 8) | infoArray[11];
    cfg->u16Width1 = (infoArray[14] << 8) | infoArray[13];
    cfg->u16Height1 = (infoArray[16] << 8) | infoArray[15];

    return 0;
}

int RequestUDIDLength(void)
{
    return 1;
}

int RequestUDIDPack(const struct RequestUDID* request, uint8_t* infoArray)
{
    if (!request || !infoArray)
    {
        printf("info array or UDID request is NULL!\n");
        return -1;
    }

    *infoArray = request->u8Cmd;

    return 0;
}

int RequestUDIDUnPack(struct RequestUDID* request, const uint8_t* infoArray, int arrayLen)
{
    if (!request || !infoArray)
    {
        printf("info array or UDID request is NULL!\n");
        return -1;
    }

    if (arrayLen != RequestUDIDLength())
    {
        printf("array len is:%d, not equal UDID request info length:%d\n", arrayLen, RequestUDIDLength());
        return -1;
    }

    request->u8Cmd = infoArray[0];

    return 0;
}

int UDIDInfoLength(void)
{
    return sizeof(struct UDIDInfo);
}

int UDIDInfoPack(const struct UDIDInfo* udid, uint8_t* infoArray)
{
    if (!udid || !infoArray)
    {
        printf("info array or UDID is NULL!\n");
        return -1;
    }

    memcpy(infoArray, udid->UDID, sizeof(struct UDIDInfo));

    return 0;
}

int UDIDInfoUnPack(struct UDIDInfo* udid, const uint8_t* infoArray, int arrayLen)
{
    if (!udid || !infoArray)
    {
        printf("info array or UDID is NULL!\n");
        return -1;
    }

    if (arrayLen != UDIDInfoLength())
    {
        printf("array len is:%d, not equal UDID info length:%d\n", arrayLen, UDIDInfoLength());
        return -1;
    }

    memcpy(udid->UDID, infoArray, arrayLen);

    return 0;
}

int DetectFpsCfgLength(void)
{
    return 1;
}

int DetectFpsCfgPack(const struct DetectFpsCfg* cfg, uint8_t* infoArray)
{
    if (!cfg || !infoArray)
    {
        printf("info array or fps cfg is NULL!\n");
        return -1;
    }

    infoArray[0] = cfg->u8Fps;

    return 0;
}

int DetectFpsCfgUnPack(struct DetectFpsCfg* cfg, const uint8_t* infoArray, int arrayLen)
{
    if (!cfg || !infoArray)
    {
        printf("info array or fps cfg is NULL!\n");
        return -1;
    }

    if (arrayLen != DetectFpsCfgLength())
    {
        printf("array len is:%d, not equal fps cfg length:%d\n", arrayLen, DetectFpsCfgLength());
        return -1;
    }

    cfg->u8Fps = infoArray[0];

    return 0;
}

int PercentagePowerLength(void)
{
    return 1;
}

int PercentagePowerPack(const struct PercentagePower* power, uint8_t* infoArray)
{
    if (!power || !infoArray)
    {
        printf("info array or percentage power is NULL!\n");
        return -1;
    }

    infoArray[0] = power->u8Percentage;

    return 0;
}

int PercentagePowerUnPack(struct PercentagePower* power, const uint8_t* infoArray, int arrayLen)
{
    if (!power || !infoArray)
    {
        printf("info array or percentage power is NULL!\n");
        return -1;
    }

    if (arrayLen != PercentagePowerLength())
    {
        printf("array len is:%d, not equal percentage power length:%d\n", arrayLen, PercentagePowerLength());
        return -1;
    }

    power->u8Percentage = infoArray[0];

    return 0;
}

int QuickMotorCtrlLength(void)
{
    return 4;
}

int QuickMotorCtrlPack(const struct QuickMotorCtrl* ctrl, uint8_t* infoArray)
{
    if (!ctrl || !infoArray)
    {
        printf("info array or quick motor ctrl is NULL!\n");
        return -1;
    }

    *infoArray++ = ctrl->i16HorizontalMotorSpeed & 0xFF;
    *infoArray++ = (ctrl->i16HorizontalMotorSpeed >> 8) & 0xFF;
    *infoArray++ = ctrl->i16VerticalMotorSpeed & 0xFF;
    *infoArray = (ctrl->i16VerticalMotorSpeed >> 8) & 0xFF;

    return 0;
}

int QuickMotorCtrlUnPack(struct QuickMotorCtrl* ctrl, const uint8_t* infoArray, int arrayLen)
{
    if (!ctrl || !infoArray)
    {
        printf("info array or quick motor ctrl is NULL!\n");
        return -1;
    }

    if (arrayLen != QuickMotorCtrlLength())
    {
        printf("array len is:%d, not equal quick motor ctrl length:%d\n", arrayLen, QuickMotorCtrlLength());
        return -1;
    }

    ctrl->i16HorizontalMotorSpeed = (infoArray[1] << 8) | infoArray[0];
    ctrl->i16VerticalMotorSpeed = (infoArray[3] << 8) | infoArray[2];

    return 0;
}

int LimitSwitchStateReportSwitchLength(void)
{
    return 1;
}

int LimitSwitchStateReportSwitchPack(const struct LimitSwitchStateReportSwitch* ctrl, uint8_t* infoArray)
{
    if (!ctrl || !infoArray)
    {
        printf("info array or limit switch state report switch ctrl is NULL!\n");
        return -1;
    }

    infoArray[0] = ctrl->u8Switch;

    return 0;
}

int LimitSwitchStateReportSwitchUnPack(struct LimitSwitchStateReportSwitch* ctrl, const uint8_t* infoArray, int arrayLen)
{
    if (!ctrl || !infoArray)
    {
        printf("info array or limit switch state report switch ctrl is NULL!\n");
        return -1;
    }

    if (arrayLen != LimitSwitchStateReportSwitchLength())
    {
        printf("array len is:%d, not equal limit switch state report switch length:%d\n", arrayLen, LimitSwitchStateReportSwitchLength());
        return -1;
    }

    ctrl->u8Switch = infoArray[0];

    return 0;
}

int LimitSwitchStateLength(void)
{
    return 2;
}

int LimitSwitchStatePack(const struct LimitSwitchState* state, uint8_t* infoArray)
{
    if (!state || !infoArray)
    {
        printf("info array or limit switch state is NULL!\n");
        return -1;
    }

    infoArray[0] = state->u8ForwardLimitSwitchState;
    infoArray[1] = state->u8BackwardLimitSwitchState;

    return 0;
}

int LimitSwitchStateUnPack(struct LimitSwitchState* state, const uint8_t* infoArray, int arrayLen)
{
    if (!state || !infoArray)
    {
        printf("info array or limit switch state is NULL!\n");
        return -1;
    }

    if (arrayLen != LimitSwitchStateLength())
    {
        printf("array len is:%d, not equal limit switch state length:%d\n", arrayLen, LimitSwitchStateLength());
        return -1;
    }

    state->u8ForwardLimitSwitchState = infoArray[0];
    state->u8BackwardLimitSwitchState = infoArray[1];

    return 0;
}


int MoveAngleLength(void)
{
    return 8;
}

int MoveAnglePack(const struct MoveAngle* angle, uint8_t* infoArray)
{
    if (!angle || !infoArray)
    {
        printf("info array or angle is NULL!\n");
        return -1;
    }

    *infoArray++ = angle->i32YawAngle & 0xFF;
    *infoArray++ = (angle->i32YawAngle >> 8) & 0xFF;
    *infoArray++ = (angle->i32YawAngle >> 16) & 0xFF;
    *infoArray++ = (angle->i32YawAngle >> 24) & 0xFF;

    *infoArray++ = angle->i32PitchAngle & 0xFF;
    *infoArray++ = (angle->i32PitchAngle >> 8) & 0xFF;
    *infoArray++ = (angle->i32PitchAngle >> 16) & 0xFF;
    *infoArray   = (angle->i32PitchAngle >> 24) & 0xFF;

    return 0;
}

int MoveAngleUnPack(struct MoveAngle* angle, const uint8_t* infoArray, int arrayLen)
{
    if (!angle || !infoArray)
    {
        printf("info array or angle is NULL!\n");
        return -1;
    }

    if (arrayLen != MoveAngleLength())
    {
        printf("array len is:%d, not equal move angle length:%d\n", arrayLen, MoveAngleLength());
        return -1;
    }

    angle->i32YawAngle = (infoArray[3] << 24) | (infoArray[2] << 16) | (infoArray[1] << 8) | infoArray[0];

    angle->i32PitchAngle = (infoArray[7] << 24) | (infoArray[6] << 16) | (infoArray[5] << 8) | infoArray[4];

    return 0;
}

int AppVersionLength(void)
{
    return 3;
}

int AppVersionPack(const struct AppVersion* version, uint8_t* infoArray)
{
    if (!version || !infoArray)
    {
        printf("info array or version is NULL!\n");
        return -1;
    }

    *infoArray++ = version->u8AppId;
    *infoArray++ = version->u8MajorId;
    *infoArray   = version->u8MinorId;

    return 0;
}

int AppVersionUnPack(struct AppVersion* version, const uint8_t* infoArray, int arrayLen)
{
    if (!version || !infoArray)
    {
        printf("info array or version is NULL!\n");
        return -1;
    }

    if (arrayLen != AppVersionLength())
    {
        printf("array len is:%d, not equal app version length:%d\n", arrayLen, AppVersionLength());
        return -1;
    }

    version->u8AppId = infoArray[0];
    version->u8MajorId = infoArray[1];
    version->u8MinorId = infoArray[2];

    return 0;
}

int KmeansRectLength(void)
{
    return 24;
}

int KmeansRectPack(const struct KmeansRect* rect, uint8_t* infoArray)
{
    if (!rect || !infoArray)
    {
        printf("info array or kmeans rect is NULL!\n");
        return -1;
    }

    *infoArray++ = rect->x0 & 0xFF;
    *infoArray++ = (uint8_t)(rect->x0 >> 8) & 0xFF;
    *infoArray++ = rect->y0 & 0xFF;
    *infoArray++ = (uint8_t)(rect->y0 >> 8) & 0xFF;
    *infoArray++ = rect->width0 & 0xFF;
    *infoArray++ = (uint8_t)(rect->width0 >> 8) & 0xFF;
    *infoArray++ = rect->height0 & 0xFF;
    *infoArray++ = (uint8_t)(rect->height0 >> 8) & 0xFF;

    *infoArray++ = rect->x1 & 0xFF;
    *infoArray++ = (uint8_t)(rect->x1 >> 8) & 0xFF;
    *infoArray++ = rect->y1 & 0xFF;
    *infoArray++ = (uint8_t)(rect->y1 >> 8) & 0xFF;
    *infoArray++ = rect->width1 & 0xFF;
    *infoArray++ = (uint8_t)(rect->width1 >> 8) & 0xFF;
    *infoArray++ = rect->height1 & 0xFF;
    *infoArray++ = (uint8_t)(rect->height1 >> 8) & 0xFF;

    *infoArray++ = rect->x2 & 0xFF;
    *infoArray++ = (uint8_t)(rect->x2 >> 8) & 0xFF;
    *infoArray++ = rect->y2 & 0xFF;
    *infoArray++ = (uint8_t)(rect->y2 >> 8) & 0xFF;
    *infoArray++ = rect->width2 & 0xFF;
    *infoArray++ = (uint8_t)(rect->width2 >> 8) & 0xFF;
    *infoArray++ = rect->height2 & 0xFF;
    *infoArray++ = (uint8_t)(rect->height2 >> 8) & 0xFF;

    return 0;
}

int KmeansRectUnPack(struct KmeansRect* rect, const uint8_t* infoArray, int arrayLen)
{
    if (!rect || !infoArray)
    {
        printf("info array or kmeans rect is NULL!\n");
        return -1;
    }

    if (arrayLen != KmeansRectLength())
    {
        printf("array len is:%d, not equal kmeans rect length:%d\n", arrayLen, KmeansRectLength());
        return -1;
    }

    rect->x0 = infoArray[0] | (infoArray[1] << 8);
    rect->y0 = infoArray[2] | (infoArray[3] << 8);
    rect->width0 = infoArray[4] | (infoArray[5] << 8);
    rect->height0 = infoArray[6] | (infoArray[7] << 8);

    rect->x1 = infoArray[8] | (infoArray[9] << 8);
    rect->y1 = infoArray[10] | (infoArray[11] << 8);
    rect->width1 = infoArray[12] | (infoArray[13] << 8);
    rect->height1 = infoArray[14] | (infoArray[15] << 8);

    rect->x2 = infoArray[16] | (infoArray[17] << 8);
    rect->y2 = infoArray[18] | (infoArray[19] << 8);
    rect->width2 = infoArray[20] | (infoArray[21] << 8);
    rect->height2 = infoArray[22] | (infoArray[23] << 8);

    return 0;
}

int ZoomInfoLength(void)
{
    return 4;
}

int ZoomInfoPack(const struct ZoomInfo* zoom, uint8_t* infoArray)
{
    if (!zoom || !infoArray)
    {
        printf("info array or zoom info is NULL!\n");
        return -1;
    }

    *infoArray++ = (uint8_t)(zoom->i32Zoom & 0xFF);
    *infoArray++ = (uint8_t)((zoom->i32Zoom >> 8) & 0xFF);
    *infoArray++ = (uint8_t)((zoom->i32Zoom >> 16) & 0xFF);
    *infoArray = (uint8_t)((zoom->i32Zoom >> 24) & 0xFF);

    return 0;
}

int ZoomInfoUnPack(struct ZoomInfo* zoom, const uint8_t* infoArray, int arrayLen)
{
    if (!zoom || !infoArray)
    {
        printf("info array or zoom info is NULL!\n");
        return -1;
    }

    if (arrayLen != ZoomInfoLength())
    {
        printf("array len is:%d, not equal zoom info length:%d\n", arrayLen, ZoomInfoLength());
        return -1;
    }

    zoom->i32Zoom = infoArray[0] | (infoArray[1] << 8) | (infoArray[2] << 16) | (infoArray[3] << 24);

    return 0;
}

int SdkCfgLength(void)
{
    return 8;
}

int SdkCfgPack(const struct SdkCfg* cfg, uint8_t* infoArray)
{
    if (!cfg || !infoArray)
    {
        printf("info array or sdk cfg is NULL!\n");
        return -1;
    }

    *infoArray++ = (uint8_t)(cfg->u16YawMaxSpeed & 0xFF);
    *infoArray++ = (uint8_t)((cfg->u16YawMaxSpeed >> 8) & 0xFF);
    *infoArray++ = (uint8_t)(cfg->u16PitchMaxSpeed & 0xFF);
    *infoArray++ = (uint8_t)((cfg->u16PitchMaxSpeed >> 8) & 0xFF);
    *infoArray++ = (uint8_t)(cfg->u16YawMaxAngle & 0xFF);
    *infoArray++ = (uint8_t)((cfg->u16YawMaxAngle >> 8) & 0xFF);
    *infoArray++ = (uint8_t)(cfg->u16PitchMaxAngle & 0xFF);
    *infoArray   = (uint8_t)((cfg->u16PitchMaxAngle >> 8) & 0xFF);

    return 0;
}

int SdkCfgUnPack(struct SdkCfg* cfg, const uint8_t* infoArray, int arrayLen)
{
    const uint8_t* srcPtr = infoArray;

    if (!cfg || !infoArray)
    {
        printf("info array or sdk cfg is NULL!\n");
        return -1;
    }

    if (arrayLen != SdkCfgLength())
    {
        printf("array len is:%d, not equal sdk cfg length:%d\n", arrayLen, SdkCfgLength());
        return -1;
    }

    cfg->u16YawMaxSpeed = (srcPtr[1] << 8) | srcPtr[0];
    srcPtr += 2;

    cfg->u16PitchMaxSpeed = (srcPtr[1] << 8) | srcPtr[0];
    srcPtr += 2;

    cfg->u16YawMaxAngle = (srcPtr[1] << 8) | srcPtr[0];
    srcPtr += 2;

    cfg->u16PitchMaxAngle = (srcPtr[1] << 8) | srcPtr[0];

    return 0;
}

int DeviceMemInfoLength(void)
{
    return 44;
}

int DeviceMemInfoPack(const struct DeviceMemInfo* info, uint8_t* infoArray)
{
    uint8_t* ptr = infoArray;
    int i = 0;
    int total_cnt = 2;

    if (!info || !infoArray)
    {
        printf("info array or device mem info is NULL!\n");
        return -1;
    }

    for (; i < total_cnt; ++i)
    {
        memcpy(ptr, info->memInfo[i].partition_name, sizeof(info->memInfo[i].partition_name));
        ptr += sizeof(info->memInfo[i].partition_name);

        *ptr++ = info->memInfo[i].u16TotalSize & 0xFF;
        *ptr++ = (uint8_t)((info->memInfo[i].u16TotalSize >> 8) & 0xFF);
        *ptr++ = info->memInfo[i].u16UsedSize & 0xFF;
        *ptr++ = (uint8_t)((info->memInfo[i].u16UsedSize >> 8) & 0xFF);
        *ptr++ = info->memInfo[i].u16AvailableSize & 0xFF;
        *ptr++ = (uint8_t)((info->memInfo[i].u16AvailableSize >> 8) & 0xFF);
    }

    return 0;
}

int DeviceMemInfoUnPack(struct DeviceMemInfo* info, const uint8_t* infoArray, int arrayLen)
{
    uint8_t* ptr = (uint8_t*)infoArray;
    int i = 0;
    int total_cnt = 2;

    if (!info || !infoArray)
    {
        printf("info array or device mem info is NULL!\n");
        return -1;
    }

    if (arrayLen != DeviceMemInfoLength())
    {
        printf("array len is:%d, not equal device mem info length:%d\n", arrayLen, DeviceMemInfoLength());
        return -1;
    }

    for (; i < total_cnt; ++i)
    {
        memcpy(info->memInfo[i].partition_name, ptr, sizeof(info->memInfo[i].partition_name));
        ptr += sizeof(info->memInfo[i].partition_name);

        info->memInfo[i].u16TotalSize = ptr[0] | (ptr[1] << 8);
        ptr += 2;
        info->memInfo[i].u16UsedSize = ptr[0] | (ptr[1] << 8);
        ptr += 2;
        info->memInfo[i].u16AvailableSize = ptr[0] | (ptr[1] << 8);
        ptr += 2;
    }

    return 0;
}




#include <stdint.h>
#include <sys/time.h>
#ifndef  __GIMBAL_MQTT_MESSAGE_H__
#define  __GIMBAL_MQTT_MESSAGE_H__

struct MotorCtrlInfo
{
    uint8_t  u8MotorId;    //motor id号
    uint8_t  u8Direction;  //转动方向
    uint16_t u16Speed;     //转动速度
    uint16_t u16Step;      //转动的步数
};

//==================================================================
//函 数 名：int  MotorCtrlInfoLength(void);
//功能描述： 获取电机控制信息的长度
//输入参数： 无
//返 回 值： 电机控制信息的长度
//==================================================================
int  MotorCtrlInfoLength(void);


//==================================================================
//函 数 名： int MotorCtrlInfoPack(const MotorCtrlInfo* ctrlInfo, uint8_t* infoArray)
//功能描述： 将电机控制信息放入infoArray指向的地址。
//输入参数： ctrlInfo：电机的控制信息，输入参数
//             infoArray: 将ctrlInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      内存长度通过MotorCtrlInfoLength()函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int MotorCtrlInfoPack(const struct MotorCtrlInfo* ctrlInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int MotorCtrlInfoUnPack(MotorCtrlInfo* ctrlInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将电机控制信息从infoArray中解析放入ctrlInfo中
//输入参数： ctrlInfo：电机的控制信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在ctrlInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int MotorCtrlInfoUnPack(struct MotorCtrlInfo* ctrlInfo, const uint8_t* infoArray, int arrayLen);



struct MotorPidInfo
{
    uint8_t u8MotorId;     //motor id号
    float   P;             //PID中的P值
    float   I;             //PID中的I值
    float   D;             //PID中的D值
};

//==================================================================
//函 数 名：int MotorPidInfoLength(void);
//功能描述： 获取电机PID信息的长度
//输入参数： 无
//返 回 值： 电机PID信息的长度
//==================================================================
int MotorPidInfoLength(void);


//==================================================================
//函 数 名：int MotorPidInfoPack(const MotorPidInfo* pidInfo, uint8_t* infoArray)
//功能描述： 将电机的PID信息放入infoArray指向的地址。
//输入参数： pidInfo：电机的PID信息，输入参数
//           infoArray: 将pidInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      内存长度通过MotorPidInfoLength()函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int MotorPidInfoPack(const struct MotorPidInfo* pidInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int MotorPidInfoUnPack(MotorPidInfo* pidInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将电机的PID信息从infoArray中解析放入pidInfo中
//输入参数： pidInfo：电机的PID信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在pidInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int MotorPidInfoUnPack(struct MotorPidInfo* pidInfo, const uint8_t* infoArray, int arrayLen);


struct AiResult    //检测结果组合
{
    uint8_t u8Conf;          //检测结果的置信度
    uint8_t u8Id;            //检测结果的类别
    uint16_t u16X0;          //检测到的方框左上角的X坐标
    uint16_t u16Y0;          //检测到的方框左上角的Y坐标
    uint16_t u16X1;          //检测到的方框右下角的X坐标
    uint16_t u16Y1;          //检测到的方框右下角的Y坐标
};


//struct AiPacketInfo这结构体，client可以不用管
struct AiPacketInfo
{
    uint8_t* infoArray;      //指向封包的内存地址，这段地址由用户自己申请释放，长度由
    //int AiPacketLength(int aiResultCount)获取

    uint8_t aiResultMaxCount;       //infoArray 最大能放置AiResult的数量,即申请infoArray时传入
    //int AiPacketLength(uint8_t aiResultCount)函数的参数

    uint8_t curCount;         //当前infoArray中放置的AiResult的数量
};


//==================================================================
//函 数 名：int AiPacketLength(uint8_t aiResultCount);
//功能描述： 获取struct AiPacketInfo中infoArray内存地址的长度
//输入参数： aiResultCount：要打包的AiResult的个数。
//返 回 值： struct AiPacketInfo中infoArray内存地址的长度
//==================================================================
int AiPacketLength(uint8_t aiResultCount);



//==================================================================
//函 数 名：int AiPacketInfoInit(struct AiPacketInfo, uint8_t* array, uint8_t aiResultCount);
//功能描述： 使用已申请的内存对struct AiPacketInfo进行初始化操作
//输入参数： info:要初始化的struct AiPacketInfo指针地址
//         array:struct AiPacketInfo使用的内存，长度由int AiPacketLength(int aiResultCount);指定
//         aiResultCount:struct AiPacketInfo最大能放置的AiResult的数量,即申请array内存时传入AiPacketLength函数的参数
//返 回 值： 0：初始化成功  -1：初始化失败
//==================================================================
int AiPacketInfoInit(struct AiPacketInfo* info, uint8_t* array, uint8_t aiResultCount);


//==================================================================
//函 数 名：int AiPacketAddResult(struct AiPacketInfo* packet, struct AiPacketInfo* result);
//功能描述： 将AiResult放入已初始化的struct AiPacketInfo中
//输入参数： packet:要封包的struct AiPacketInfo指针
//           result：ai检测结果
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int AiPacketAddResult(struct AiPacketInfo* packet, const struct AiResult* result);


struct AiResultIterator
{
    uint8_t* u8PacketArray;        //指向要解析的内存地址
    uint8_t  resultCount;           //u8PacketArray中包含的AiResult的个数，由库内部解析
    int      curResultIndex;            //当前所指向的AiResult,由库维护
    struct AiResult  curResult;         //当前返回给用户的struct AiResult，由库内部维护
};

struct LogHead
{
    int16_t oriPktLength;
    int16_t yawAngle;
    int16_t pitchAngle;
    int8_t pktIndex;
    int64_t timeStamp;
};

#define MAX_LOG_PKT_LEN 220 /* 如果超过220个字节，需要分两次传输 */
#define MAX_AI_RES_NUM ((MAX_LOG_PKT_LEN - 16) / 10) /* 一个包能包含的最多AI检测结果 */

typedef void (*AiLogSplitCallback)(uint8_t *message, uint16_t length);
typedef void (*AiLogReassembleCallback)(struct AiResult *aiResArr, uint16_t aiResNum, struct LogHead* headPtr);

typedef struct {
    struct AiResult *aiResbuffer; /* 用来暂存AI检测结果 */
    size_t aiResbufferSize;
    uint8_t aiResHandledCnt; /* 已暂存的AI检测结果个数 */
    struct LogHead *logHead; /* 用来暂存日志头，等待后续数据上来决定是继续暂存或调用回调 */
    uint8_t subPktNum; /* 由oriPktLength决定 */
    int8_t state; /* state=0...n, n < subPktNum, 表示在等待第n个subpacket, state = -1表示未初始化 */
    uint8_t **packetCache; /* 纠正乱序输入 */
    uint32_t lossPktNum; /* 记录丢掉的包 */
    AiLogReassembleCallback  callback;
} AiLogReassembler;

//==================================================================
//函 数 名：void  AiResultIteratorInit(struct AiResultIterator* iter, uint8_t* packetArray);
//功能描述： 使用接收到的包含struct AiResult的内存packetArray对struct AiResultIterator进行初始化操作
//输入参数： iter：要初始化的struct AiResultIterator地址
//         packetArray：接收到的包含AiResult的内存数据
//返 回 值：0：初始化成功  -1：初始化失败
//==================================================================
int  AiResultIter(struct AiResultIterator* iter, uint8_t* packetArray);


//==================================================================
//函 数 名：struct AiResultIterator* AiResultIterNext(struct AiResult* result);
//功能描述： 从struct AiResultIterator中获取下一个struct AiResult地址。注意，返回的地址只有在下一次调用
//           struct AiResultIterator* AiResultIterNext(struct AiResult* result)之前有效
//输入参数： iter：包含struct AiResult结果的struct AiResultIterator地址
//返 回 值：0：获取成功  -1：获取失败
//==================================================================
struct AiResult* AiResultIterNext(struct AiResultIterator* iter);


//==================================================================
//函 数 名： int AiLogPack(struct AiResult* inputAiResArr, uint8_t aiResCnt, struct LogHead* headPtr, AiLogCallback callback);
//功能描述： 根据AI检测结果打包AI日志数据包
//输入参数： inputAiResArr：完整的AI检测结果， aiResCnt：AI检测结果个数，headPtr: 帧头指针, callback: 对拆分后的小包进行处理的函数
//返 回 值：0：获取成功  -1：获取失败
//==================================================================
int AiLogSplit(struct AiResult* inputAiResArr, uint8_t aiResCnt, struct LogHead* headPtr, AiLogSplitCallback callback);

//==================================================================
//函 数 名：int AiLogReassemblerInit(AiLogReassembler* reassembler, size_t aiResbufferSize, AiLogReassembleCallback callback)
//功能描述： 初始化AiLogReassembler对象
//输入参数： aiResbufferSize：AI检测结果暂存区大小，callback：对完整AI检测结果序列进行处理的函数
//返 回 值：重组器对象指针，失败返回NULL
//==================================================================
int AiLogReassemblerInit(AiLogReassembler* reassembler, size_t aiResbufferSize, AiLogReassembleCallback callback);

//==================================================================
//函 数 名：int AiLogReassemble(AiLogReassembler *reassembler, uint8_t* inputBuffer, uint32_t inputLen);
//功能描述： 重组AI日志数据包
//输入参数： reassembler：AiLogReassembler对象， inputBuffer：AI日志数据包， inputLen：AI日志数据包长度
//返 回 值：0：重组成功  -1：重组失败
//使用说明：每收到一个AI日志数据包调用一次该函数，内部检测到多个小数据包可以重组为一个完整数据包时，自动调用callback对完整数据包进行处理
//==================================================================
int AiLogReassemble(AiLogReassembler *reassembler, uint8_t* inputBuffer, uint32_t inputLen);

//==================================================================
//函 数 名：void AiLogReassemblerDestroy(AiLogReassembler *reassembler);
//功能描述： 销毁AiLogReassembler对象
//输入参数： reassembler：AiLogReassembler对象
//返 回 值：无
//使用说明：程序结束时调用
//==================================================================
void AiLogReassemblerDestroy(AiLogReassembler *reassembler);

struct AiSwitch
{
    uint8_t u8AiSwitch;  //控制AI打开关闭。 0：关闭AI， 1：开启AI
};

//==================================================================
//函 数 名：int AiSwitchInfoLength(void);
//功能描述： 获取AI开关信息的长度
//输入参数： 无
//返 回 值： AI开关信息的长度
//==================================================================
int AiSwitchInfoLength(void);


//==================================================================
//函 数 名：int AiSwitchInfoPack(const AiSwitch* switchInfo, uint8_t* infoArray)
//功能描述： 将AI开关信息放入infoArray指向的地址。
//输入参数： switchInfo：ai的开关信息，输入参数
//           infoArray: 将switchInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过AiSwitchInfoLength()函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int AiSwitchInfoPack(const struct AiSwitch* switchInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int AiSwitchInfoUnPack(AiSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将AI开关信息从infoArray中解析放入switchInfo中
//输入参数： switchInfo：ai的开关信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在pidInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int AiSwitchInfoUnPack(struct AiSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);



struct TrackingSwitch
{
    uint8_t u8TrackingSwitch;      //控制是否使用目标跟踪。0：关闭目标跟踪；1：使用目标跟踪
};

//==================================================================
//函 数 名：int TrackingSwitchInfoLength(void);
//功能描述： 获取目标跟踪开关信息的长度
//输入参数： 无
//返 回 值： 目标跟踪开关信息的长度
//==================================================================
int TrackingSwitchInfoLength(void);


//==================================================================
//函 数 名：int TrackingSwitchInfoPack(const TrackingSwitch* switchInfo, uint8_t* infoArray)
//功能描述： 将目标跟踪开关信息放入infoArray指向的地址。
//输入参数： switchInfo：目标跟踪的开关信息，输入参数
//           infoArray: 将switchInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过TrackingSwitchInfoLength()函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int TrackingSwitchInfoPack(const struct TrackingSwitch* switchInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int TrackingSwitchInfoUnPack(TrackingSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将目标跟踪开关信息从infoArray中解析放入switchInfo中
//输入参数： switchInfo：目标跟踪的开关信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在switchInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int TrackingSwitchInfoUnPack(struct TrackingSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);


struct MotorDecisionSwitch
{
    uint8_t u8DecisionSwitch;             //控制当前由谁控制电机转动。   0：由AI控制   1：由USER控制
};


//==================================================================
//函 数 名：int MotorDecisionSwitchInfoLength(void);
//功能描述： 获取电机决策开关信息的长度
//输入参数： 无
//返 回 值： 电机决策开关信息的长度
//==================================================================
int MotorDecisionSwitchInfoLength(void);


//==================================================================
//函 数 名：int MotorDecisionSwitchInfoPack(const MotorDecisionSwitch* switchInfo, uint8_t* infoArray)
//功能描述： 将电机决策开关信息放入infoArray指向的地址。
//输入参数： switchInfo：电机决策的开关信息，输入参数
//           infoArray: 将switchInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过MotorDecisionSwitchInfoLength()函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int MotorDecisionSwitchInfoPack(const struct MotorDecisionSwitch* switchInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int MotorDecisionSwitchInfoUnPack(MotorDecisionSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将电机决策开关信息从infoArray中解析放入switchInfo中
//输入参数： switchInfo：电机决策开关信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在switchInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int MotorDecisionSwitchInfoUnPack(struct MotorDecisionSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);


struct SoftPowerOffSwitch
{
    uint8_t u8SoftPowerOffSwitch;             //软件关机控制。    1：进行软件关机
};


//==================================================================
//函 数 名：int SoftPowerOffSwitchInfoLength(void);
//功能描述： 获取软件关机信息的长度
//输入参数： 无
//返 回 值： 软件关机信息的长度
//==================================================================
int SoftPowerOffSwitchInfoLength(void);


//==================================================================
//函 数 名：int SoftPowerOffSwitchInfoPack(const SoftPowerOffSwitch* switchInfo, uint8_t* infoArray)
//功能描述： 将软件关机信息放入infoArray指向的地址。
//输入参数： switchInfo：软件关机信息，输入参数
//           infoArray: 将switchInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过SoftPowerOffSwitchInfoLength()函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int SoftPowerOffSwitchInfoPack(const struct SoftPowerOffSwitch* switchInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int SoftPowerOffSwitchInfoUnPack(SoftPowerOffSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将软件关机开关信息从infoArray中解析放入switchInfo中
//输入参数： switchInfo：软件关机信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在switchInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int SoftPowerOffSwitchInfoUnPack(struct SoftPowerOffSwitch* switchInfo, const uint8_t* infoArray, int arrayLen);



struct BluetoothPairingStatus
{
    uint8_t u8Status;      //蓝牙配对状态。 0:蓝牙未配对  1:蓝牙配对中   2:蓝牙配对成功
};

//==================================================================
//函 数 名：int BluetoothPairingStatusLength(void);
//功能描述： 获取蓝牙配对状态信息的长度
//输入参数： 无
//返 回 值： 蓝牙配对状态信息的长度
//==================================================================
int BluetoothPairingStatusLength(void);


//==================================================================
//函 数 名： BluetoothPairingStatusPack(const struct BluetoothPairingStatus* statusInfo, uint8_t* infoArray);
//功能描述： 将蓝牙配对状态信息放入infoArray指向的地址。
//输入参数： statusInfo：蓝牙配对状态信息，输入参数
//           infoArray: 将statusInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过BluetoothPairingStatusLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int BluetoothPairingStatusPack(const struct BluetoothPairingStatus* statusInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int BluetoothPairingStatusUnPack(struct BluetoothPairingStatus* statusInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将蓝牙配对状态信息从infoArray中解析放入statusInfo中
//输入参数： statusInfo：蓝牙配对状态信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在statusInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int BluetoothPairingStatusUnPack(struct BluetoothPairingStatus* statusInfo, const uint8_t* infoArray, int arrayLen);




struct BluetoothUpgrade
{
    uint8_t u8FirmwarePath[128];
};


//==================================================================
//函 数 名：int BluetoothUpgradeLength(struct BluetoothUpgrade* upgrade);
//功能描述： 获取蓝牙升级信息的长度
//输入参数： 无
//返 回 值： 0获取长度失败， 非0:蓝牙升级信息的长度
//==================================================================
int BluetoothUpgradeLength(struct BluetoothUpgrade* upgrade);


//==================================================================
//函 数 名： BluetoothUpgradePack(const struct BluetoothUpgrade* upgradeInfo, uint8_t* infoArray);
//功能描述： 将蓝牙升级信息放入infoArray指向的地址。
//输入参数： upgradeInfo：蓝牙升级信息，输入参数
//           infoArray: 将upgradeInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过BluetoothUpgradeLength(struct BluetoothUpgrade* upgrade)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int BluetoothUpgradePack(const struct BluetoothUpgrade* upgradeInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int BluetoothUpgradeUnPack(struct BluetoothUpgrade* upgradeInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将蓝牙升级信息从infoArray中解析放入upgradeInfo中
//输入参数： upgradeInfo：蓝牙升级信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在upgradeInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int BluetoothUpgradeUnPack(struct BluetoothUpgrade* upgradeInfo, const uint8_t* infoArray, int arrayLen);


struct BatteryLevel
{
    uint16_t u16BatteryLevel;
};

//==================================================================
//函 数 名：int BatteryLevelLength(void);
//功能描述： 获取电量信息的长度
//输入参数： 无
//返 回 值： 0获取长度失败， 非0:电量信息的长度
//==================================================================
int BatteryLevelLength(void);


//==================================================================
//函 数 名： BatteryLevelPack(const struct BatteryLevel* levelInfo, uint8_t* infoArray);
//功能描述： 将电量放入infoArray指向的地址。
//输入参数： upgradeInfo: 蓝牙升级信息，输入参数
//           infoArray: 将levelInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过BatteryLevelLength(void)函数获取
//返 回 值： 0: 打包成功  -1: 打包失败
//==================================================================
int BatteryLevelPack(const struct BatteryLevel* levelInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int BatteryLevelUnPack(struct BatteryLevel* levelInfo, const uint8_t* infoArray, int arrayLen)
//功能描述： 将电量信息从infoArray中解析放入levelInfo中
//输入参数： levelInfo: 电量信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在levelInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int BatteryLevelUnPack(struct BatteryLevel* levelInfo, const uint8_t* infoArray, int arrayLen);

/// <struct SideLightInfo>
/// 侧灯信息
/// 0:  开机默认状态，此时蓝牙未连接。侧灯呼吸闪烁
/// 1:  运行状态，此时蓝牙已连接，可以接收到其他命令, 比如录像，拍照命令。侧灯常亮
/// 2:  休眠状态，如果在1状态下持续时间超过60秒，且60秒内没有收到新的1信息，会自动进去此状态。侧灯熄灭
//  3:  唤醒状态，这个状态会让侧灯闪烁快速闪烁两下，然后就进入运行状态。
/// 4:  错误状态，运行出现问题，此状态不可切换到其他状态。侧灯急速闪烁
struct SideLightInfo
{
    uint8_t u8SideLightInfo;
};

//==================================================================
//函 数 名：int SideLightInfoLength(void);
//功能描述： 侧灯信息的长度
//输入参数： 无
//返 回 值： 0获取长度失败， 非0:侧灯信息的长度
//==================================================================
int SideLightInfoLength(void);


//==================================================================
//函 数 名： int SideLightInfoPack(const struct SideLightInfo* lightInfo, uint8_t* infoArray);
//功能描述： 将侧灯信息放入infoArray指向的地址。
//输入参数： lightInfo: 侧灯信息，输入参数
//           infoArray: 将lightInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过SideLightInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int SideLightInfoPack(const struct SideLightInfo* lightInfo, uint8_t* infoArray);


//==================================================================
//函 数 名：int SideLightInfoUnPack(struct SideLightInfo* lightInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将侧灯信息从infoArray中解析放入lightInfo中
//输入参数： lightInfo: 侧灯信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在lightInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int SideLightInfoUnPack(struct SideLightInfo* lightInfo, const uint8_t* infoArray, int arrayLen);



/// <struct BluetoothName>
/// 蓝牙名字
struct BluetoothNameInfo
{
    uint8_t u8Name[64];
};

//==================================================================
//函 数 名：int BluetoothNameInfoLength(void);
//功能描述： 蓝牙名字信息的长度
//输入参数： 无
//返 回 值： 0获取长度失败， 非0:蓝牙名字信息的长度
//==================================================================
int BluetoothNameInfoLength(void);


//==================================================================
//函 数 名： int BluetoothNameInfoPack(const struct BluetoothNameInfo* nameInfo, uint8_t* infoArray);
//功能描述： 将蓝牙名字信息放入infoArray指向的地址。
//输入参数： nameInfo：蓝牙名字信息，输入参数
//           infoArray: 将nameInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过BluetoothNameInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int BluetoothNameInfoPack(const struct BluetoothNameInfo* nameInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int BluetoothNameInfoUnPack(struct BluetoothNameInfo* nameInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将蓝牙名字信息从infoArray中解析放入nameInfo中
//输入参数： nameInfo: 蓝牙名字信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在nameInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int BluetoothNameInfoUnPack(struct BluetoothNameInfo* nameInfo, const uint8_t* infoArray, int arrayLen);


/// <struct MotorPosition>
/// 电机位置
struct MotorPosition
{
    uint8_t u8MotorId;
    int32_t i32Position;
};

//==================================================================
//函 数 名：int MotorPositionLength(void);
//功能描述： 电机位置信息的长度
//输入参数： 无
//返 回 值： 0获取长度失败， 非0:电机位置信息的长度
//==================================================================
int MotorPositionLength(void);


//==================================================================
//函 数 名： int MotorPositionPack(const struct MotorPosition* positionInfo, uint8_t* infoArray);
//功能描述： 将电机位置信息放入infoArray指向的地址。
//输入参数： positionInfo：电机位置信息，输入参数
//           infoArray: 将positionInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过MotorPositionLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int MotorPositionPack(const struct MotorPosition* positionInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int MotorPositionUnPack(struct MotorPosition* positionInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将电机位置信息从infoArray中解析放入positionInfo中
//输入参数： positionInfo: 电机位置信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在positionInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0：解包成功  -1：解包失败
//==================================================================
int MotorPositionUnPack(struct MotorPosition* positionInfo, const uint8_t* infoArray, int arrayLen);



/// <struct BluetoothHidden>
/// 蓝牙隐藏开关
struct BluetoothHiddenSwitch
{
    uint8_t u8Hidden;   // 1: 隐藏蓝牙(无法被发现和连接)  0:不隐藏
};

//==================================================================
//函 数 名：int BluetoothHiddenSwitchLength(void);
//功能描述： 蓝牙隐藏开关的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:蓝牙隐藏开关的长度
//==================================================================
int BluetoothHiddenSwitchLength(void);


//==================================================================
//函 数 名： int BluetoothHiddenSwitchPack(const struct BluetoothHiddenSwitch* hiddenSwitch, uint8_t* infoArray);
//功能描述： 将蓝牙隐藏开关信息放入infoArray指向的地址。
//输入参数： hiddenSwitch：蓝牙隐藏开关信息，输入参数
//           infoArray: 将hiddenSwitch中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过BluetoothHiddenSwitchLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int BluetoothHiddenSwitchPack(const struct BluetoothHiddenSwitch* hiddenSwitch, uint8_t* infoArray);


//==================================================================
//函 数 名： int BluetoothHiddenSwitchUnPack(struct BluetoothHiddenSwitch* hiddenSwitch, const uint8_t* infoArray, int arrayLen);
//功能描述： 将蓝牙隐藏开关信息从infoArray中解析放入hiddenSwitch中
//输入参数： hiddenSwitch: 蓝牙隐藏开关信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在hiddenSwitch中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int BluetoothHiddenSwitchUnPack(struct BluetoothHiddenSwitch* hiddenSwitch, const uint8_t* infoArray, int arrayLen);




/// <struct ChargeDetectInfo>
/// 充电检测信息
struct ChargeDetectInfo
{
    uint8_t u8ChargeInfo;   // 0: 当前没有充电(没有电源接入)  1: 当前有充电(有电源接入)
};

//==================================================================
//函 数 名：int ChargeDetectInfoLength(void);
//功能描述： 充电检测信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:充电检测信息的长度
//==================================================================
int ChargeDetectInfoLength(void);


//==================================================================
//函 数 名： int ChargeDetectInfoPack(const struct ChargeDetectInfo* chargeInfo, uint8_t* infoArray);
//功能描述： 将充电检测信息放入infoArray指向的地址。
//输入参数： chargeInfo：充电检测信息，输入参数
//           infoArray: 将chargeInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过ChargeDetectInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int ChargeDetectInfoPack(const struct ChargeDetectInfo* chargeInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int ChargeDetectInfoUnPack(struct ChargeDetectInfo* chargeInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将充电检测信息从infoArray中解析放入chargeInfo中
//输入参数： chargeInfo: 充电检测信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在chargeInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int ChargeDetectInfoUnPack(struct ChargeDetectInfo* chargeInfo, const uint8_t* infoArray, int arrayLen);



//数据传输发起信息结构体
struct TransferRequest
{
    uint8_t u8TransferFileName[64];     //要传输的数据文件名
    uint32_t u32TransferTotalLength;     //要传输的数据文件总大小
    uint32_t u32TotalPacketCnt;         //要传输的总数据包数量
    uint8_t u8PacketSize;              //每个数据包的大小，除了最后一个数据包
};

//请求从特定的数据包id开始传输结构体
struct TransferRequestData
{
    uint32_t u32Id;                   //要重新传输的数据包id编号;传输数据的数据编号从这个数据编号开始
};


//传输数据的结构体
struct TransferData
{
    uint32_t u32Id;             //传输的ID号，对同一个数据文件，传一次就+1
    uint8_t u8DataSize;        //这个包的数据大小
    uint8_t* data;             //要传输的数据，长度需要和数据长度匹配
};


struct TransferCmd
{
    uint8_t u8Cmd;
    //0: 开始请求传输 发送struct TransferRequest结构体 由手机端发送
    //1: 回复传输可以开始                          有嵌入式端发送
    //2: 请求特定的数据包   发送struct TransferRequestData结构体   由嵌入式端发送
    //3: 传输数据   发送struct TransferData结构体  由手机发送
    //4: 传输结束                                  由嵌入式端发送,此时完成文件传输
    //5: 传输结束                                  由手机发送(此时还没有真的传完，需要嵌入式端查看有哪些包没有传成功,
    //                                             将这些包ID发送给手机，由手机将这些包继续发送
    //6: 传输异常，传输过程异常，传输终止，必须重新开始   两边都可以发送
    //传输数据格式为    |cmd|   |数据结构体|   如果cmd=1, 4,5和6，后面就不跟数据结构体
};

//注意：这些Pack和UnPack函数，内部都是包含了cmd以及对应的结构体数据
int TransferRequestLength();
int TransferRequestPack(const struct TransferRequest* requestInfo, uint8_t* infoArray);
int TransferRequestUnPack(struct TransferRequest* requestInfo, const uint8_t* infoArray, int arrayLen);

int TransferRequestDataLength();
int TransferRequestDataPack(const struct TransferRequestData* requestDataInfo, uint8_t* infoArray);
int TransferRequestDataUnPack(struct TransferRequestData* requestDataInfo, const uint8_t* infoArray, int arrayLen);

int TransferDataLength(uint8_t u8DataSize);
int TransferDataPack(const struct TransferData* dataInfo, uint8_t* infoArray);
int TransferDataUnPack(struct TransferData* dataInfo, const uint8_t* infoArray, int arrayLen);


/// <struct TemperatureInfo>
/// 温度信息,单位℃
struct TemperatureInfo
{
    uint8_t u8Temperature;
};

//==================================================================
//函 数 名：int TemperatureInfoLength(void);
//功能描述： 温度信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:温度信息的长度
//==================================================================
int TemperatureInfoLength(void);


//==================================================================
//函 数 名： int TemperatureInfoPack(const struct TemperatureInfo* tempInfo, uint8_t* infoArray);
//功能描述： 将温度信息放入infoArray指向的地址。
//输入参数： tempInfo：温度信息，输入参数
//           infoArray: 将tempInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过TemperatureInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int TemperatureInfoPack(const struct TemperatureInfo* tempInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int TemperatureInfoUnPack(struct TemperatureInfo* tempInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将温度信息从infoArray中解析放入tempInfo中
//输入参数： tempInfo: 温度信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在tempInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int TemperatureInfoUnPack(struct TemperatureInfo* tempInfo, const uint8_t* infoArray, int arrayLen);


/// <struct DebugCtrlInfo>
/// log控制信息   //只有在u8Cmd等于1时，i64TimeStamp才有效
struct DebugCtrlInfo
{
    uint8_t u8Cmd;
    int64_t i64TimeStamp;
};

//==================================================================
//函 数 名：int DebugCtrlInfoLength(void);
//功能描述： log控制信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:log控制信息的长度
//==================================================================
int DebugCtrlInfoLength(void);


//==================================================================
//函 数 名： int DebugCtrlInfoPack(const struct DebugCtrlInfo* debugInfo, uint8_t* infoArray);
//功能描述： 将log控制信息放入infoArray指向的地址。
//输入参数： debugInfo：log控制信息，输入参数
//           infoArray: 将debugInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过DebugCtrlInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int DebugCtrlInfoPack(const struct DebugCtrlInfo* debugInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int DebugCtrlInfoUnPack(struct DebugCtrlInfo* debugInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将log控制信息从infoArray中解析放入debugInfo中
//输入参数： debugInfo: log控制信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在debugInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int DebugCtrlInfoUnPack(struct DebugCtrlInfo* debugInfo, const uint8_t* infoArray, int arrayLen);



/// <struct FirmwareUpdateInfo>
/// 固件升级信息
struct FirmwareUpdateInfo
{
    uint8_t u8FirmwareName[64];   //要进行升级的固件文件名
};

//==================================================================
//函 数 名：int FirmwareUpdateInfoLength(void);
//功能描述： 固件升级信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:固件升级信息的长度
//==================================================================
int FirmwareUpdateInfoLength(void);


//==================================================================
//函 数 名： int FirmwareUpdateInfoPack(const struct FirmwareUpdateInfo* updateInfo, uint8_t* infoArray);
//功能描述： 将固件升级信息放入infoArray指向的地址。
//输入参数： updateInfo：固件升级信息，输入参数
//           infoArray: 将updateInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过FirmwareUpdateInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int FirmwareUpdateInfoPack(const struct FirmwareUpdateInfo* updateInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int FirmwareUpdateInfoUnPack(struct FirmwareUpdateInfo* updateInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将固件升级信息从infoArray中解析放入updateInfo中
//输入参数： updateInfo: 固件升级信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在updateInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int FirmwareUpdateInfoUnPack(struct FirmwareUpdateInfo* updateInfo, const uint8_t* infoArray, int arrayLen);



/// <struct FirmwareUpdateStatus>
/// 固件升级状态
struct FirmwareUpdateStatus
{
    uint8_t u8UpdateStatus;  //0:升级成功  1:升级失败
};

//==================================================================
//函 数 名：int FirmwareUpdateStatusLength(void);
//功能描述： 固件升级状态
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:固件升级状态的长度
//==================================================================
int FirmwareUpdateStatusLength(void);


//==================================================================
//函 数 名： int FirmwareUpdateStatusPack(const struct FirmwareUpdateStatus* updateStaus, uint8_t* infoArray);
//功能描述： 将固件升级状态信息放入infoArray指向的地址。
//输入参数： updateStaus：固件升级状态信息，输入参数
//           infoArray: 将updateStaus中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过FirmwareUpdateStatusLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int FirmwareUpdateStatusPack(const struct FirmwareUpdateStatus* updateStaus, uint8_t* infoArray);


//==================================================================
//函 数 名： int FirmwareUpdateStatusUnPack(struct FirmwareUpdateStatus* updateStatus, const uint8_t* infoArray, int arrayLen);
//功能描述： 将固件升级状态信息从infoArray中解析放入updateStatus中
//输入参数： updateStatus: 固件升级状态信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在updateStatus中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int FirmwareUpdateStatusUnPack(struct FirmwareUpdateStatus* updateStatus, const uint8_t* infoArray, int arrayLen);


/// <struct BluetoothVersionInfo>
/// 蓝牙的版本信息
struct BluetoothVersionInfo
{
    uint8_t u8MajorId;   //主版本号
    uint8_t u8MinorId;   //次版本号
};

//==================================================================
//函 数 名：int BluetoothVersionInfoLength(void);
//功能描述： 蓝牙版本信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:蓝牙版本信息的长度
//==================================================================
int BluetoothVersionInfoLength(void);


//==================================================================
//函 数 名： int BluetoothVersionInfoPack(const struct BluetoothVersionInfo* versionInfo, uint8_t* infoArray);
//功能描述： 将蓝牙版本信息放入infoArray指向的地址。
//输入参数： versionInfo：蓝牙版本信息，输入参数
//           infoArray: 将versionInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过BluetoothVersionInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int BluetoothVersionInfoPack(const struct BluetoothVersionInfo* versionInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int BluetoothVersionInfoUnPack(struct BluetoothVersionInfo* versionInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将蓝牙版本信息从infoArray中解析放入versionInfo中
//输入参数： versionInfo: 蓝牙版本信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在versionInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int BluetoothVersionInfoUnPack(struct BluetoothVersionInfo* versionInfo, const uint8_t* infoArray, int arrayLen);



/// <struct FirmwareVersionInfo>
/// 固件的版本信息
struct FirmwareVersionInfo
{
    uint16_t u16BluetoothMajorId;      //蓝牙固件的主版本号
    uint16_t u16BluetoothMinorId;      //蓝牙固件的次版本号

    uint16_t u16FirmwareHardwareId;   //变色龙固件的硬件版本号
    uint16_t u16FirmwareSystemId;     //变色龙固件的系统版本号
    uint16_t u16FirmwareAppId;        //变色龙固件的应用版本号
};

//==================================================================
//函 数 名：int FirmwareVersionInfoLength(void);
//功能描述： 固件版本信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:固件版本信息的长度
//==================================================================
int FirmwareVersionInfoLength(void);


//==================================================================
//函 数 名： int FirmwareVersionInfoPack(const struct FirmwareVersionInfo* versionInfo, uint8_t* infoArray);
//功能描述： 将固件版本信息放入infoArray指向的地址。
//输入参数： versionInfo：固件版本信息，输入参数
//           infoArray: 将versionInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过FirmwareVersionInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int FirmwareVersionInfoPack(const struct FirmwareVersionInfo* versionInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int FirmwareVersionInfoUnPack(struct FirmwareVersionInfo* versionInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将固件版本信息从infoArray中解析放入versionInfo中
//输入参数： versionInfo: 固件版本信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在versionInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int FirmwareVersionInfoUnPack(struct FirmwareVersionInfo* versionInfo, const uint8_t* infoArray, int arrayLen);



/// <struct FirmwareVersionRequest>
/// 请求获取固件的版本信息
struct FirmwareVersionRequest
{
    uint8_t u8Request;  //1:请求获取固件的版本信息
};

//==================================================================
//函 数 名：int FirmwareVersionRequestLength(void);
//功能描述： 请求获取固件的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:请求获取固件的长度
//==================================================================
int FirmwareVersionRequestLength(void);


//==================================================================
//函 数 名： int FirmwareVersionRequestPack(const struct FirmwareVersionRequest* request, uint8_t* infoArray);
//功能描述： 将请求信息放入infoArray指向的地址。
//输入参数： request：请求信息，输入参数
//           infoArray: 将request中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过FirmwareVersionRequestLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int FirmwareVersionRequestPack(const struct FirmwareVersionRequest* request, uint8_t* infoArray);


//==================================================================
//函 数 名： int FirmwareVersionRequestUnPack(struct FirmwareVersionRequest* request, const uint8_t* infoArray, int arrayLen);
//功能描述： 将请求信息从infoArray中解析放入request中
//输入参数： request: 请求信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在request中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int FirmwareVersionRequestUnPack(struct FirmwareVersionRequest* request, const uint8_t* infoArray, int arrayLen);


/// <struct ResetBluetooth>
/// 蓝牙重启复位
struct ResetBluetooth
{
    uint8_t u8Cmd;  //1:蓝牙重启复位
};

//==================================================================
//函 数 名：int ResetBluetoothLength(void);
//功能描述： 蓝牙重启复位命令的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:蓝牙重启复位命令的长度
//==================================================================
int ResetBluetoothLength(void);


//==================================================================
//函 数 名： int ResetBluetoothPack(const struct ResetBluetooth* resetInfo, uint8_t* infoArray);
//功能描述： 将蓝牙重启复位命令放入infoArray指向的地址。
//输入参数： resetInfo：蓝牙重启复位命令，输入参数
//           infoArray: 将resetInfo中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过ResetBluetoothLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int ResetBluetoothPack(const struct ResetBluetooth* resetInfo, uint8_t* infoArray);


//==================================================================
//函 数 名： int ResetBluetoothUnPack(struct ResetBluetooth* resetInfo, const uint8_t* infoArray, int arrayLen);
//功能描述： 将蓝牙重启复位命令从infoArray中解析放入resetInfo中
//输入参数： resetInfo: 蓝牙重启复位命令，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在resetInfo中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int ResetBluetoothUnPack(struct ResetBluetooth* resetInfo, const uint8_t* infoArray, int arrayLen);


/// <struct OtgChargeCfg>
/// 对外放电和对内充电状态配置
struct OtgChargeCfg
{
    uint8_t u8Cfg;  //0:对内充电   1:对外放电
};

//==================================================================
//函 数 名：int OtgChargeCfgLength(void);
//功能描述： 充放电配置的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:充放电配置的长度
//==================================================================
int OtgChargeCfgLength(void);


//==================================================================
//函 数 名： int OtgChargeCfgPack(const struct OtgChargeCfg* cfg, uint8_t* infoArray);
//功能描述： 将充放电配置放入infoArray指向的地址。
//输入参数： cfg：充放电配置，输入参数
//           infoArray: 将cfg中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过OtgChargeCfgLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int OtgChargeCfgPack(const struct OtgChargeCfg* cfg, uint8_t* infoArray);


//==================================================================
//函 数 名： int OtgChargeCfgUnPack(struct OtgChargeCfg* cfg, const uint8_t* infoArray, int arrayLen);
//功能描述： 将充放电配置从infoArray中解析放入cfg中
//输入参数： cfg: 充放电配置，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在cfg中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int OtgChargeCfgUnPack(struct OtgChargeCfg* cfg, const uint8_t* infoArray, int arrayLen);


/// <struct DetectRoiCfg>
/// 检测时的ROI配置
struct DetectRoiCfg
{
    uint8_t u8RoiNum;   //1:  只有xxx0相关的坐标有效     2:  xxx0和xxx1相关的坐标都有效

    uint16_t u16X0;     //ROI的x坐标
    uint16_t u16Y0;     //ROI的y坐标
    uint16_t u16Width0;   //ROI的宽
    uint16_t u16Height0;   //ROI的高

    uint16_t u16X1;
    uint16_t u16Y1;
    uint16_t u16Width1;
    uint16_t u16Height1;
};

//==================================================================
//函 数 名：int  DetectRoiCfgLength(void);
//功能描述： 检测时ROI配置的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:检测时ROI配置的长度
//==================================================================
int DetectRoiCfgLength(void);


//==================================================================
//函 数 名： int DetectRoiCfgPack(const struct DetectRoiCfg* cfg, uint8_t* infoArray);
//功能描述： 将检测时ROI配置放入infoArray指向的地址。
//输入参数： cfg:检测时ROI配置，输入参数
//           infoArray: 将cfg中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过DetectRoiCfgLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int DetectRoiCfgPack(const struct DetectRoiCfg* cfg, uint8_t* infoArray);


//==================================================================
//函 数 名： int DetectRoiCfgUnPack(struct DetectRoiCfg* cfg, const uint8_t* infoArray, int arrayLen);
//功能描述： 将检测时ROI配置从infoArray中解析放入cfg中
//输入参数： cfg: 检测时ROI配置，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在cfg中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int DetectRoiCfgUnPack(struct DetectRoiCfg* cfg, const uint8_t* infoArray, int arrayLen);


/// <struct RequestUDID>
/// 请求获取设备的唯一识别码
struct RequestUDID
{
    uint8_t u8Cmd;  //1:请求获取设备的唯一识别码
};

//==================================================================
//函 数 名：int RequestUDIDLength(void);
//功能描述： 请求获取设备的唯一识别码命令的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:获取设备的唯一识别码命令的长度
//==================================================================
int RequestUDIDLength(void);


//==================================================================
//函 数 名： int RequestUDIDPack(const struct RequestUDID* request, uint8_t* infoArray);
//功能描述： 将设备的唯一识别码的请求命令放入infoArray指向的地址。
//输入参数： request：请求指令，输入参数
//           infoArray: 将request中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过RequestUDIDLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int RequestUDIDPack(const struct RequestUDID* request, uint8_t* infoArray);


//==================================================================
//函 数 名： int RequestUDIDUnPack(struct RequestUDID* request, const uint8_t* infoArray, int arrayLen);
//功能描述： 将设备的唯一识别码的请求命令从infoArray中解析放入request中
//输入参数： request: 设备的唯一识别码的请求命令，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在request中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int RequestUDIDUnPack(struct RequestUDID* request, const uint8_t* infoArray, int arrayLen);


/// <struct UDIDInfo>
/// 设备的唯一识别码
struct UDIDInfo
{
    uint8_t UDID[16];  //设备的唯一识别码
};

//==================================================================
//函 数 名：int UDIDInfoLength(void);
//功能描述： 设备的唯一识别码命令的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:设备的唯一识别码命令的长度
//==================================================================
int UDIDInfoLength(void);


//==================================================================
//函 数 名： int UDIDInfoPack(const struct UDIDInfo* udid, uint8_t* infoArray);
//功能描述： 将设备的唯一识别码放入infoArray指向的地址。
//输入参数： udid：设备的唯一识别码，输入参数
//           infoArray: 将udid中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过UDIDInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int UDIDInfoPack(const struct UDIDInfo* udid, uint8_t* infoArray);


//==================================================================
//函 数 名： int UDIDInfoUnPack(struct UDIDInfo* udid, const uint8_t* infoArray, int arrayLen);
//功能描述： 将设备的唯一识别码从infoArray中解析放入udid中
//输入参数： udid: 设备的唯一识别码，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在udid中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int UDIDInfoUnPack(struct UDIDInfo* udid, const uint8_t* infoArray, int arrayLen);



/// <struct DetectFpsCfg>
/// 检测时的帧率控制
struct DetectFpsCfg
{
    uint8_t u8Fps;  //非0：期望的fps值   0：不做帧率控制
};

//==================================================================
//函 数 名：int DetectFpsCfgLength(void);
//功能描述： 帧率控制指令的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:帧率控制指令的长度
//==================================================================
int DetectFpsCfgLength(void);


//==================================================================
//函 数 名： int DetectFpsCfgPack(const struct DetectFpsCfg* cfg, uint8_t* infoArray);
//功能描述： 将帧率控制指令放入infoArray指向的地址。
//输入参数： cfg：帧率控制配置，输入参数
//           infoArray: 将cfg中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过DetectFpsCfgLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int DetectFpsCfgPack(const struct DetectFpsCfg* cfg, uint8_t* infoArray);


//==================================================================
//函 数 名： int DetectFpsCfgUnPack(struct DetectFpsCfg* cfg, const uint8_t* infoArray, int arrayLen);
//功能描述： 将帧率控制指令从infoArray中解析放入cfg中
//输入参数： cfg: 帧率控制配置，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在cfg中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int DetectFpsCfgUnPack(struct DetectFpsCfg* cfg, const uint8_t* infoArray, int arrayLen);


/// <struct PercentagePower>
/// 百分比电量
struct PercentagePower
{
    uint8_t u8Percentage;  //百分比电量，范围0~100
};

//==================================================================
//函 数 名：int PercentagePowerLength(void);
//功能描述： 百分比电量的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:百分比电量的长度
//==================================================================
int PercentagePowerLength(void);


//==================================================================
//函 数 名： int PercentagePowerPack(const struct PercentagePower* power, uint8_t* infoArray);
//功能描述： 将百分比电量放入infoArray指向的地址。
//输入参数： power：百分比电量，输入参数
//           infoArray: 将cfg中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过PercentagePowerLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int PercentagePowerPack(const struct PercentagePower* power, uint8_t* infoArray);


//==================================================================
//函 数 名： int PercentagePowerUnPack(struct PercentagePower* power, const uint8_t* infoArray, int arrayLen);
//功能描述： 将百分比电量从infoArray中解析放入power中
//输入参数： power: 百分比电量，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在power中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int PercentagePowerUnPack(struct PercentagePower* power, const uint8_t* infoArray, int arrayLen);



/// <struct QuickMotorCtrl>
/// 快速电机控制
struct QuickMotorCtrl
{
    int16_t i16HorizontalMotorSpeed;  //水平电机控制速度  正对应向前  负对应向后  0代表停
    int16_t i16VerticalMotorSpeed;    //垂直电机控制速度
};

//==================================================================
//函 数 名：int QuickMotorCtrlLength(void);
//功能描述： 快速电机控制的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:快速电机控制的长度
//==================================================================
int QuickMotorCtrlLength(void);


//==================================================================
//函 数 名： int QuickMotorCtrlPack(const struct QuickMotorCtrl* ctrl, uint8_t* infoArray);
//功能描述： 将快速电机控制放入infoArray指向的地址。
//输入参数： ctrl：快速电机控制，输入参数
//           infoArray: 将ctrl中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过QuickMotorCtrlLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int QuickMotorCtrlPack(const struct QuickMotorCtrl* ctrl, uint8_t* infoArray);


//==================================================================
//函 数 名： int QuickMotorCtrlUnPack(struct QuickMotorCtrl* ctrl, const uint8_t* infoArray, int arrayLen);
//功能描述： 将快速电机控制从infoArray中解析放入ctrl中
//输入参数： ctrl: 快速电机控制，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在ctrl中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int QuickMotorCtrlUnPack(struct QuickMotorCtrl* ctrl, const uint8_t* infoArray, int arrayLen);



/// <struct LimitSwitchStateReportSwitch>
/// 限位开关状态上报开关
struct LimitSwitchStateReportSwitch
{
    uint8_t u8Switch;   //0:关闭限位开关状态上报     1:开启限位开关状态上报
};

//==================================================================
//函 数 名：int LimitSwitchStateReportSwitchLength(void);
//功能描述： 限位开关状态上报开关的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:限位开关状态上报开关的长度
//==================================================================
int LimitSwitchStateReportSwitchLength(void);


//==================================================================
//函 数 名： int LimitSwitchStateReportSwitchPack(const struct LimitSwitchStateReportSwitch* ctrl, uint8_t* infoArray);
//功能描述： 将限位开关状态上报开关信息放入infoArray指向的地址。
//输入参数： ctrl：限位开关状态上报开关信息，输入参数
//           infoArray: 将ctrl中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过LimitSwitchStateReportSwitchLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int LimitSwitchStateReportSwitchPack(const struct LimitSwitchStateReportSwitch* ctrl, uint8_t* infoArray);


//==================================================================
//函 数 名： int LimitSwitchStateReportSwitchUnPack(struct LimitSwitchStateReportSwitch* ctrl, const uint8_t* infoArray, int arrayLen);
//功能描述： 将限位开关状态上报开关信息从infoArray中解析放入ctrl中
//输入参数： ctrl: 限位开关状态上报开关信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在ctrl中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int LimitSwitchStateReportSwitchUnPack(struct LimitSwitchStateReportSwitch* ctrl, const uint8_t* infoArray, int arrayLen);



/// <struct LimitSwitchState>
/// 限位开关状态
struct LimitSwitchState
{
    uint8_t u8ForwardLimitSwitchState;   //前向限位开关状态。0:开关未触发    1:开关触发
    uint8_t u8BackwardLimitSwitchState;  //后向限位开关状态。0:开关未触发    1:开关触发
};

//==================================================================
//函 数 名：int LimitSwitchStateLength(void);
//功能描述： 限位开关状态的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:限位开关状态的长度
//==================================================================
int LimitSwitchStateLength(void);


//==================================================================
//函 数 名： int LimitSwitchStatePack(const struct LimitSwitchState* state, uint8_t* infoArray);
//功能描述： 将限位开关状态放入infoArray指向的地址。
//输入参数： state：限位开关状态，输入参数
//           infoArray: 将state中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过LimitSwitchStateLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int LimitSwitchStatePack(const struct LimitSwitchState* state, uint8_t* infoArray);


//==================================================================
//函 数 名： int LimitSwitchStateUnPack(struct LimitSwitchState* state, const uint8_t* infoArray, int arrayLen);
//功能描述： 将限位开关状态从infoArray中解析放入state中
//输入参数： state: 限位开关状态，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在state中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int LimitSwitchStateUnPack(struct LimitSwitchState* state, const uint8_t* infoArray, int arrayLen);


/// <struct MoveAngle>
/// 角度状态
struct MoveAngle
{
    int i32YawAngle;      //水平的角度状态。单位: 0.001°
    int i32PitchAngle;    //垂直的角度状态。单位: 0.001°
};

//==================================================================
//函 数 名：int MoveAngleLength(void);
//功能描述： 角度状态的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:角度状态的长度
//==================================================================
int MoveAngleLength(void);


//==================================================================
//函 数 名： int MoveAnglePack(const struct MoveAngle* angle, uint8_t* infoArray);
//功能描述： 将角度状态放入infoArray指向的地址。
//输入参数： angle：角度状态，输入参数
//           infoArray: 将angle中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过MoveAngleLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int MoveAnglePack(const struct MoveAngle* angle, uint8_t* infoArray);


//==================================================================
//函 数 名： int MoveAngleUnPack(struct MoveAngle* angle, const uint8_t* infoArray, int arrayLen);
//功能描述： 将角度状态从infoArray中解析放入angle中
//输入参数： angle: 角度状态，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在angle中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int MoveAngleUnPack(struct MoveAngle* angle, const uint8_t* infoArray, int arrayLen);


/// <struct AppVersion>
/// 应用版本信息
struct AppVersion
{
    uint8_t u8AppId;    //应用id
    uint8_t u8MajorId;  //应用的主版本号
    uint8_t u8MinorId;  //应用的次版本号
};

//==================================================================
//函 数 名：int AppVersionLength(void);
//功能描述： 应用版本的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:应用版本的长度
//==================================================================
int AppVersionLength(void);


//==================================================================
//函 数 名： int AppVersionPack(const struct AppVersion* version, uint8_t* infoArray);
//功能描述： 将应用版本信息放入infoArray指向的地址。
//输入参数： version：应用版本信息，输入参数
//           infoArray: 将version中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过AppVersionLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int AppVersionPack(const struct AppVersion* version, uint8_t* infoArray);


//==================================================================
//函 数 名： int AppVersionUnPack(struct AppVersion* version, const uint8_t* infoArray, int arrayLen);
//功能描述： 将应用版本信息从infoArray中解析放入version中
//输入参数： version: 应用版本信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在version中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int AppVersionUnPack(struct AppVersion* version, const uint8_t* infoArray, int arrayLen);

struct KmeansRect
{
    uint16_t x0;
    uint16_t y0;
    uint16_t width0;
    uint16_t height0;

    uint16_t x1;
    uint16_t y1;
    uint16_t width1;
    uint16_t height1;

    uint16_t x2;
    uint16_t y2;
    uint16_t width2;
    uint16_t height2;
};


int KmeansRectLength(void);

int KmeansRectPack(const struct KmeansRect* rect, uint8_t* infoArray);

int KmeansRectUnPack(struct KmeansRect* rect, const uint8_t* infoArray, int arrayLen);



/// <struct ZoomInfo>
/// Zoom信息
struct ZoomInfo
{
    int32_t i32Zoom;   //zoom大小，实际的zoom大小需要再除于1000
};

//==================================================================
//函 数 名：int ZoomInfoLength(void);
//功能描述： Zoom信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:Zoom信息的长度
//==================================================================
int ZoomInfoLength(void);


//==================================================================
//函 数 名： int ZoomInfoPack(const struct ZoomInfo* zoom, uint8_t* infoArray);
//功能描述： 将Zoom信息放入infoArray指向的地址。
//输入参数： zoom：Zoom信息，输入参数
//           infoArray: 将zoom中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过ZoomInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int ZoomInfoPack(const struct ZoomInfo* zoom, uint8_t* infoArray);


//==================================================================
//函 数 名： int ZoomInfoUnPack(struct ZoomInfo* zoom, const uint8_t* infoArray, int arrayLen);
//功能描述： 将Zoom信息从infoArray中解析放入zoom中
//输入参数： zoom: Zoom信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在zoom中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int ZoomInfoUnPack(struct ZoomInfo* zoom, const uint8_t* infoArray, int arrayLen);


/// <struct SdkCfg>
/// sdk配置信息
struct SdkCfg
{
    uint16_t u16YawMaxSpeed;   //yaw最大速度
    uint16_t u16PitchMaxSpeed;  //pitch最大速度
    uint16_t u16YawMaxAngle;    //yaw最大角度,单位为°
    uint16_t u16PitchMaxAngle;  //pitch最大角度,单位为°
};

//==================================================================
//函 数 名：int SdkCfgLength(void);
//功能描述： sdk配置信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:sdk配置信息的长度
//==================================================================
int SdkCfgLength(void);


//==================================================================
//函 数 名： int SdkCfgPack(const struct SdkCfg* cfg, uint8_t* infoArray);
//功能描述： 将sdk配置信息放入infoArray指向的地址。
//输入参数： cfg：sdk配置信息，输入参数
//           infoArray: 将cfg中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过SdkCfgLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int SdkCfgPack(const struct SdkCfg* cfg, uint8_t* infoArray);


//==================================================================
//函 数 名： int SdkCfgUnPack(struct SdkCfg* cfg, const uint8_t* infoArray, int arrayLen);
//功能描述： 将sdk配置信息从infoArray中解析放入cfg中
//输入参数： cfg：sdk配置信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在cfg中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int SdkCfgUnPack(struct SdkCfg* cfg, const uint8_t* infoArray, int arrayLen);



//存储空间信息
struct  MemInfo
{
    char partition_name[16];
    uint16_t u16TotalSize; //总空间大小，实际值需要/10.f,得到以MB为单位的空间大小
    uint16_t u16UsedSize;  //已使用空间大小，实际值需要/10.f,得到以MB为单位的空间大小
    uint16_t u16AvailableSize;  //可用空间大小，实际值需要/10.f,得到以MB为单位的空间大小
};

//变色龙设备的内部存储空间状况
struct  DeviceMemInfo
{
    struct MemInfo memInfo[2];  //目前就两个分区/oem和/userdata
};

//==================================================================
//函 数 名：int DeviceMemInfoLength(void);
//功能描述： 内部存储空间信息的长度
//输入参数： 无
//返 回 值： 0:获取长度失败， 非0:内部存储空间信息的长度
//==================================================================
int DeviceMemInfoLength(void);


//==================================================================
//函 数 名： int DeviceMemInfoPack(const struct DeviceMemInfo* info, uint8_t* infoArray);
//功能描述： 将内部存储空间信息放入infoArray指向的地址。
//输入参数： info：内部存储空间信息，输入参数
//           infoArray: 将info中的信息填入infoArray中。在传入前infoArray的内存需要提前分配好，
//                      地址长度通过DeviceMemInfoLength(void)函数获取
//返 回 值： 0：打包成功  -1：打包失败
//==================================================================
int DeviceMemInfoPack(const struct  DeviceMemInfo* info, uint8_t* infoArray);


//==================================================================
//函 数 名： int DeviceMemInfoUnPack(struct DeviceMemInfo* info, const uint8_t* infoArray, int arrayLen);
//功能描述： 将内部存储空间信息从infoArray中解析放入info中
//输入参数： info：内部存储空间信息，输出参数
//           infoArray: 将infoArray中的数据进行解析，结果放在info中
//           arrayLen: infoArray指向内存的长度。
//返 回 值： 0: 解包成功  -1: 解包失败
//==================================================================
int DeviceMemInfoUnPack(struct DeviceMemInfo* info, const uint8_t* infoArray, int arrayLen);

#endif

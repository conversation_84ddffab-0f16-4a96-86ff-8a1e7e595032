package com.blink.blinkfocos.db;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.blink.blinkfocos.App;
import com.blink.blinkfocos.entity.FolderDbEntity;
import com.blink.blinkfocos.entity.ImageDbEntity;
import com.blink.blinkfocos.entity.VideoDbEntity;

@Database(entities = {VideoDbEntity.class, FolderDbEntity.class, ImageDbEntity.class},version = 2,exportSchema = false)
public abstract class VideoDatabase extends RoomDatabase {
    public abstract VideoDao videoDao();
    public abstract ImageDao imageDao();
    public abstract FolderDao folderDao();

    public static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            database.execSQL("ALTER TABLE VideoDbEntity ADD COLUMN uuid TEXT");
            database.execSQL("ALTER TABLE VideoDbEntity ADD COLUMN isCut TEXT");
            database.execSQL("ALTER TABLE VideoDbEntity ADD COLUMN dottedAddress TEXT");
            // 创建 FolderDbEntity 表
            database.execSQL("CREATE TABLE IF NOT EXISTS `FolderDbEntity` "
                    + "(`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "
                    + "`name` TEXT, "
                    + "`fileLocation` TEXT, "
                    + "`address` TEXT, "
                    + "`base64` TEXT, "
                    + "`createTime` TEXT, "
                    + "`mobileVision` TEXT, "
                    + "`systemVision` TEXT, "
                    + "`appVison` TEXT, "
                    + "`folderUUID` TEXT, "
                    + "`count` INTEGER NOT NULL)"); // 设置默认值为0

            // 创建 ImageDbEntity 表
            database.execSQL("CREATE TABLE IF NOT EXISTS `ImageDbEntity` "
                    + "(`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "
                    + "`name` TEXT, "
                    + "`fileLocation` TEXT, "
                    + "`isUpload` TEXT, "
                    + "`address` TEXT, "
                    + "`captureTime` TEXT, "
                    + "`logAddress` TEXT, "
                    + "`mobileVision` TEXT, "
                    + "`systemVision` TEXT, "
                    + "`appVison` TEXT, "
                    + "`folderUUID` TEXT, "
                    + "`videoUUID` TEXT, "
                    + "`modelType` TEXT)");

        }
    };
}

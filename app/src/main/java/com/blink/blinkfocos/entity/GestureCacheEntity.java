package com.blink.blinkfocos.entity;

import android.graphics.PointF;
import android.graphics.RectF;

public class GestureCacheEntity {
    private PointF centerPoint;
    private int haveGestureDetect = 0;
    private PointF lastCenterTopPoint;

    public GestureCacheEntity(PointF centerPoint, int haveGestureDetect, PointF lastCenterTopPoint) {
        this.centerPoint = centerPoint;
        this.haveGestureDetect = haveGestureDetect;
        this.lastCenterTopPoint = lastCenterTopPoint;
    }

    public PointF getCenterPoint() {
        return centerPoint;
    }

    public void setCenterPoint(PointF centerPoint) {
        this.centerPoint = centerPoint;
    }

    public int getHaveGestureDetect() {
        return haveGestureDetect;
    }

    public void setHaveGestureDetect(int haveGestureDetect) {
        this.haveGestureDetect = haveGestureDetect;
    }

    public PointF getLastCenterTopPoint() {
        return lastCenterTopPoint;
    }

    public void setLastCenterTopPoint(PointF lastCenterTopPoint) {
        this.lastCenterTopPoint = lastCenterTopPoint;
    }
}

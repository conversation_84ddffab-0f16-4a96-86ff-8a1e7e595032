package com.blink.blinkfocos.entity;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ImageListEntity {


    @SerializedName("code")
    public int code;
    @SerializedName("pageSize")
    public int pageSize;
    @SerializedName("pageNum")
    public int pageNum;
    @SerializedName("totalRecord")
    public int totalRecord;
    @SerializedName("resultList")
    public List<ResultListDTO> resultList;

    public static class ResultListDTO {
        @SerializedName("id")
        public String id;
        @SerializedName("thumbnailUrl")
        public String thumbnailUrl;
        @SerializedName("photoUrl")
        public String photoUrl;
        @SerializedName("groupCode")
        public String groupCode;
    }
}

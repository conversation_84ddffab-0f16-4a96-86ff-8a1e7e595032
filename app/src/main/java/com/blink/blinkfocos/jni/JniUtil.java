package com.blink.blinkfocos.jni;

import com.blink.blinkfocos.entity.Recognition;

import java.nio.ByteBuffer;

public class JniUtil {
    static {
        System.loadLibrary("myapplication");
    }
    public static native Recognition[] nms(ByteBuffer recognitionBuffer, int outputSize1, int outputSize2, float detectThreshold, float iouThreshold, int inputWidth, int inputHeight, int labelId);
    public static native Recognition[] nmsForAll(ByteBuffer recognitionBuffer, int outputSize1, int outputSize2, float detectThreshold, float iouThreshold, int inputWidth, int inputHeight);
    public static native Recognition[] nmsWithhead(ByteBuffer recognitionBuffer, int outputSize1, int outputSize2, float detectThreshold, float iouThreshold, int inputWidth, int inputHeight);
    public static native Recognition[] nmsWithAll(ByteBuffer recognitionBuffer, int outputSize1, int outputSize2, float detectThreshold, float iouThreshold, int inputWidth, int inputHeight);

}

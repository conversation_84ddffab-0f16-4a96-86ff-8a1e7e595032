package com.blink.blinkfocos.module;

import static com.aries.library.fast.delegate.FastMainTabDelegate.SAVED_INSTANCE_STATE_CURRENT_TAB;
import static com.meishe.logic.constant.PagerConstants.FROM_MAIN_PAGE;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;

import android.Manifest;
import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.app.Activity;
import android.app.Dialog;
import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.LocaleList;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.arialyy.aria.core.Aria;
import com.aries.library.fast.FastManager;
import com.aries.library.fast.entity.FastTabEntity;
import com.aries.library.fast.i.QuitAppControl;
import com.aries.library.fast.manager.LoggerManager;
import com.aries.library.fast.manager.RxJavaManager;
import com.aries.library.fast.module.activity.FastMainActivity;
import com.aries.library.fast.retrofit.FastObserver;
import com.aries.library.fast.util.FastStackUtil;
import com.aries.library.fast.util.FastUtil;
import com.aries.library.fast.util.LanguageUtils;
import com.aries.library.fast.util.SPUtil;
import com.aries.library.fast.util.ToastUtil;
import com.aries.ui.view.tab.CommonTabLayout;
import com.aries.ui.view.tab.listener.OnTabSelectListener;
import com.blink.blinkfocos.App;
import com.blink.blinkfocos.BuildConfig;
import com.blink.blinkfocos.R;
import com.blink.blinkfocos.constant.SPConstant;
import com.blink.blinkfocos.module.gallery.usecase.VideoDatabaseUseCaseShare;
import com.blink.libshare.GlobalConstant;
import com.blink.blinkfocos.entity.BroadcastEntity;
import com.blink.blinkfocos.entity.ChangeLanguageEvent;
import com.blink.blinkfocos.entity.EditTaskTimerEvent;
import com.blink.blinkfocos.entity.LoadAllFinishEvent;
import com.blink.blinkfocos.entity.StartAITimerEvent;
import com.blink.blinkfocos.entity.VideoCloudItemEntity;
import com.blink.blinkfocos.module.gallery.GalleryFragment;
import com.blink.blinkfocos.module.gallery.constant.GalleryRvCheckState;
import com.blink.blinkfocos.module.gallery.viewmodel.CloudFragmentViewModel;
import com.blink.blinkfocos.module.gallery.viewmodel.LocalFragmentViewModel;
import com.blink.blinkfocos.module.live.DetectorActivity;
import com.blink.blinkfocos.module.live.LiveListFragment;
import com.blink.blinkfocos.module.live.detection.DetectionControl;
import com.blink.blinkfocos.module.local.TransferActivity;
import com.blink.blinkfocos.module.login.LoginPasswordActivity;
import com.blink.blinkfocos.module.machine.MachineFragment;
import com.blink.blinkfocos.module.my.FirmWareUpdateActivity;
import com.blink.blinkfocos.module.my.MyFragment;
import com.blink.blinkfocos.module.share.ShareActivity;
import com.blink.blinkfocos.retrofit.repository.ApiRepository;
import com.blink.blinkfocos.singleton.bluetooth.BluetoothManager;
import com.blink.blinkfocos.singleton.bluetooth.gen2.Gen2Gimbal;
import com.blink.blinkfocos.util.ClickUtil;
import com.blink.blinkfocos.util.IdManager;
import com.blink.blinkfocos.util.LogFileWriter;
import com.blink.blinkfocos.util.MySetting;
import com.blink.blinkfocos.util.PermissionUtil;
import com.blink.blinkfocos.util.Util;
import com.blink.blinkfocos.widget.GuideMask;
import com.blink.blinkfocos.widget.MyDialog;
import com.blink.libshare.GlobalConstant;
import com.blink.libshare.MobclickAgentUtil;
import com.deep.foresight.libspi.BusinessDataProviderMgr;
import com.deep.foresight.libspi.ILocalMediaProvider;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.dialog.PictureLoadingDialog;
import com.luck.picture.lib.permissions.PermissionChecker;
import com.luck.picture.lib.utils.ToastUtils;
import com.meishe.base.bean.MediaData;
import com.meishe.myvideoapp.fragment.NewMainFragment;
import com.trello.rxlifecycle3.android.ActivityEvent;
import com.umeng.analytics.MobclickAgent;
import com.umeng.socialize.ShareAction;
import com.umeng.socialize.UMShareAPI;
import com.umeng.socialize.UMShareListener;
import com.umeng.socialize.bean.SHARE_MEDIA;
import com.umeng.socialize.media.UMImage;
import com.umeng.socialize.media.UMWeb;
import com.umeng.socialize.shareboard.ShareBoardConfig;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import io.reactivex.Flowable;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class MainActivity extends FastMainActivity {
    private static final int REQUEST_ENABLE_BT = 123;
    private static final int REQUEST_ENABLE_BT_BONDED = 222;
    private static final int REQUEST_ENABLE_BT_MONITORED = 333;
    String[] permissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_SCAN,};
    String[] lowPermissions = new String[]{Manifest.permission.ACCESS_FINE_LOCATION,};
    CommonTabLayout mTabLayout;
    private final ArrayList<AppCompatImageView> bottomList = new ArrayList<>();
    private final ArrayList<AppCompatImageView> imageList = new ArrayList<>();
    private final ArrayList<AppCompatTextView> textList = new ArrayList<>();
    private final ArrayList<View> clickableTabViews = new ArrayList<>();
    private final int[] imageSelect = new int[]{R.drawable.ic_home_machine_select, R.drawable.ic_home_local_select, R.drawable.ic_home_live_select, R.drawable.ic_home_my_select};
    private final int[] imageUnSelect = new int[]{R.drawable.ic_home_machine_unselect, R.drawable.ic_home_local_unselect, R.drawable.ic_home_live_unselect, R.drawable.ic_home_my_unselect};

    private int bottomPosition;
    private AppCompatButton iv_home_video;
    private MachineFragment machineFragment;
    private GalleryFragment galleryFragment;
    private NewMainFragment newMainFragment;
    private View view_main_bottom;
    private LinearLayoutCompat rl_main_bottom;
    private LinearLayoutCompat main_live_bottom;
    private TextView tv_live_bottom_downlaod;
    private TextView tv_live_bottom_uplaod;
    private TextView tv_live_bottom_delete;
    private TextView tv_live_bottom_add_video;
    private TextView tv_live_bottom_share;
    private UMShareListener mShareListener;
    private ShareAction mShareAction;
    private ShareAction shareAction;
    private MyDialog shareOnlyOneDialog;
    protected boolean mIsFirstBack = true;
    protected long mDelayBack = 2000;


    private Disposable subscribe;
    private LiveListFragment cloudFileFragment;
    private MyFragment myFragment;
    private QuitAppControl mQuitAppControl;
    private Thread thread;
    private View view_add_video;
    private View view_bottom_share;
    private MyDialog broadcastDialog;
    private MyDialog addAlbumDialog;
    private MyDialog deleteVideoDialog;
    private Dialog mLoadingDialog;
    private MyDialog logigDialog;
    private MyDialog multiSelectShareDialog;
    private MyDialog lowStorageDialog;


    private MainActivityViewModel viewModel;
    private LocalFragmentViewModel localGalleryViewModel;
    private CloudFragmentViewModel cloudGalleryViewModel;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void setEvent(ChangeLanguageEvent changeLanguageEvent) {
        if (changeLanguageEvent.getMessage()) {
            LanguageUtils.recreate(this);
        }
    }
    @Override
    public void setRequestedOrientation(int requestedOrientation) {
        super.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
    }

    @Override
    public void beforeInitView(Bundle savedInstanceState) {
        super.beforeInitView(savedInstanceState);
        Aria.init(getApplicationContext());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void setEvent(LoadAllFinishEvent loadAllFinishEvent) {
        mTabLayout.setCurrentTab(1);
        viewModel.changeGalleryTab(1);
    }

    @Override
    public void applyOverrideConfiguration(Configuration overrideConfiguration) {
        if (overrideConfiguration != null) {
            int uiMode = overrideConfiguration.uiMode;
            overrideConfiguration.setTo(getBaseContext().getResources().getConfiguration());
            overrideConfiguration.uiMode = uiMode;
        }
        super.applyOverrideConfiguration(overrideConfiguration);
    }

    @Override
    public int getContentLayout() {
        return R.layout.activity_main;
    }

    private final BroadcastReceiver bluetoothStateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            final String action = intent.getAction();
            if (BluetoothAdapter.ACTION_STATE_CHANGED.equals(action)) {
                int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
                if (state == BluetoothAdapter.STATE_OFF) {
                    // 蓝牙已关闭，通知断开，兼容红米，Pixel手机
                    BluetoothManager.getInstance().onBlueToothOff();
                }
            }
        }
    };

    @Override
    public void initView(Bundle savedInstanceState) {
        viewModel = new ViewModelProvider(this).get(MainActivityViewModel.class);
        localGalleryViewModel = new ViewModelProvider(this).get(LocalFragmentViewModel.class);
        cloudGalleryViewModel = new ViewModelProvider(this).get(CloudFragmentViewModel.class);
        initViews();
        initListener();
        initDialog();
        getAppVersion();
        initLocation();
        setupLiveModelListener();
        LogFileWriter.initLogFile();
        LogFileWriter.startLogcatCapture();
        boolean haveGuide = (boolean) SPUtil.get(mContext, SPConstant.SP_KEY_HAVE_GUIDE, false);
        if (!haveGuide) {
            showGuide();
        }
        IntentFilter filter = new IntentFilter(BluetoothAdapter.ACTION_STATE_CHANGED);
        registerReceiver(bluetoothStateReceiver, filter);

        if (mTabLayout != null) {
            bottomPosition = mTabLayout.getCurrentTab();
        }
        bottomList.get(bottomPosition).setVisibility(View.VISIBLE);
        imageList.get(bottomPosition).setImageResource(imageSelect[bottomPosition]);
        textList.get(bottomPosition).setVisibility(View.INVISIBLE);

        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                quitApp();
            }
        });
    }

    private void initLocation() {
        GlobalConstant.address = ZoneId.systemDefault().toString();

    }

    private void setupLiveModelListener() {
        viewModel.getCheckState().observe(this, galleryRvCheckState -> {
            if (galleryRvCheckState == GalleryRvCheckState.Uncheckable) {
                bottomGone();
                liveBottomEnable(false);
            } else if (galleryRvCheckState == GalleryRvCheckState.Checkable) {
                bottomVisible(viewModel.getGalleryTab().getValue());
            }
        });

        localGalleryViewModel.getVideoCheckList().observe(this, videoCloudItemEntities -> {
            liveBottomEnable(!videoCloudItemEntities.isEmpty());
        });

        cloudGalleryViewModel.getVideoCheckList().observe(this, videoCloudItemEntities -> {
            liveBottomEnable(!videoCloudItemEntities.isEmpty());
        });
        localGalleryViewModel.getOpenTransferActivityTrigger().observe(this, unitLiveDataTriggerEvent -> {
            if (unitLiveDataTriggerEvent.getContentIfNotHandled() != null) {
                viewModel.setCheckState(GalleryRvCheckState.Uncheckable);
                Bundle bundle = new Bundle();
                bundle.putInt("position", TransferActivity.UPLOAD);
                bundle.putInt("type", 1);
                FastUtil.startActivity(mContext, TransferActivity.class, bundle);
            }
        });
    }

    private void getAppVersion() {
        String releaseVersion = Build.VERSION.RELEASE; // 用户可见的版本号，例如"10"
        String versionName = FastUtil.getVersionName(this);
        if (!viewModel.isBroadcastDialogHasShow()) {
            ApiRepository.getInstance().broadcast("Android" + releaseVersion, versionName).compose(bindUntilEvent(ActivityEvent.DESTROY)).subscribe(new FastObserver<BroadcastEntity>() {
                @Override
                public void _onNext(BroadcastEntity entity) {
                    IdManager idManager = new IdManager(MainActivity.this);
                    broadcastDialog = new MyDialog(MainActivity.this, MyDialog.BROADCAST, entity.title, entity.value, entity.frame);
                    if (idManager.containsId(entity.id)) {
                        // 执行不存在 ID 时的操作
                        Log.i(TAG, "存在ID，不进行任何操作");
                        return;
                    }
                    switch (entity.frame) {
                        case 0: //了解
                        case 1: //确认
                        case 2://不显示
                            broadcastDialog.show();
                            break;
                        case 3://确认
                            idManager.saveId(entity.id);
                            broadcastDialog.show();
                            Log.i(TAG, "弹出broadcastDialog");
                            break;
                    }
                    broadcastDialog.setCallback(() -> viewModel.setBroadcastDialogHasShow(true));
                    broadcastDialog.setCancelCallback(() -> viewModel.setBroadcastDialogHasShow(true));
                }
            });
        }
    }


    private void initDialog() {
        mShareListener = new CustomShareListener(this);
        shareOnlyOneDialog = new MyDialog(mContext, R.string.confirm, R.string.only_share_one, MyDialog.CONTENT);
        shareOnlyOneDialog.setCallback(this::continueShare);
        addAlbumDialog = new MyDialog(mContext, R.string.confirm, R.string.load_to_album, MyDialog.CONTENT);
        addAlbumDialog.setCallback(this::addAlbumDialogCallback);
        deleteVideoDialog = new MyDialog(mContext, R.string.confirm, R.string.delete_checked_video, MyDialog.CONTENT);
        deleteVideoDialog.setCallback(this::deleteVideos);
        logigDialog = new MyDialog(mContext, R.string.confirm, R.string.please_sign_when_upload, MyDialog.CONTENT);
        logigDialog.setCallback(() -> FastUtil.startActivity(mContext, LoginPasswordActivity.class));
        //TODO: 等跟内存弹窗合并之后写这里
//        lowStorageDialog = new MyDialog(mContext, R.string.empty, R.string.download_low_storage, DOWNLOAD_LOW_STORAGE);
    }

    private void deleteVideos() {
        localGalleryViewModel.deleteVideos(
                () -> {
                    Log.i(TAG, "删除成功");
                    viewModel.setCheckState(GalleryRvCheckState.Uncheckable);
                    return null;
                }, throwable -> {
                    ToastUtil.show(throwable.getMessage());
                    return null;
                }
        );
    }


    private void addAlbumDialogCallback() {
        if (Objects.requireNonNull(localGalleryViewModel.getVideoCheckList().getValue()).isEmpty()) {
            return;
        }
        mLoadingDialog = new PictureLoadingDialog(mContext);
        mLoadingDialog.show();
        localGalleryViewModel.saveVideoToGallery(mContext, () -> {
            try {
                if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                    mLoadingDialog.dismiss();
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                mLoadingDialog = null;
            }
            ToastUtil.show(mContext.getText(R.string.save_video_in_photos));
            viewModel.setCheckState(GalleryRvCheckState.Uncheckable);

            return null;
        }, throwable -> {
            try {
                if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                    mLoadingDialog.dismiss();
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            } finally {
                mLoadingDialog = null;
            }
            ToastUtil.show(throwable.getMessage());
            viewModel.setCheckState(GalleryRvCheckState.Uncheckable);
            return null;
        }, () -> {
            //TODO: 等跟内存弹窗合并之后写这里
//            lowStorageDialog.show();
            return null;
        });

    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        if (mFastMainTabDelegate != null) {
            if (mFastMainTabDelegate.mListFastTab != null) {
                machineFragment = (MachineFragment) mFastMainTabDelegate.mListFastTab.get(0).mFragment;
                galleryFragment = (GalleryFragment) mFastMainTabDelegate.mListFastTab.get(1).mFragment;
                cloudFileFragment = (LiveListFragment) mFastMainTabDelegate.mListFastTab.get(2).mFragment;
                myFragment = (MyFragment) mFastMainTabDelegate.mListFastTab.get(3).mFragment;
            }
            if (this.getSavedInstanceState() != null) {
                bottomPosition = mSavedInstanceState.getInt(SAVED_INSTANCE_STATE_CURRENT_TAB);
                mTabLayout.setCurrentTab(bottomPosition);
                if (bottomList != null) {
                    for (int i = 0; i < bottomList.size(); i++) {
                        if (i != bottomPosition) {
                            bottomList.get(i).setVisibility(View.INVISIBLE);
                        }
                    }
                    bottomList.get(bottomPosition).setVisibility(View.VISIBLE);

                }
                if (imageList != null) {
                    for (int i = 0; i < imageList.size(); i++) {
                        if (i != bottomPosition) {
                            imageList.get(i).setImageResource(imageUnSelect[i]);
                        }
                    }
                    imageList.get(bottomPosition).setImageResource(imageSelect[bottomPosition]);
                }

                if (textList != null) {
                    for (int i = 0; i < textList.size(); i++) {
                        if (i != bottomPosition) {
                            textList.get(i).setVisibility(View.VISIBLE);
                        }
                    }
                    textList.get(bottomPosition).setVisibility(View.INVISIBLE);
                }
            }
        }
    }
    public void showSportsPop() {
        if (BluetoothManager.getInstance().isCurrentGen2()) {
            BluetoothManager.getInstance().setMtu(500, null, null);
        }
        FastUtil.startActivity(mContext, SportSelectActivity.class);
    }

    private void initViews() {
        mTabLayout = findViewById(R.id.tabLayout_commonFastLib);
        main_live_bottom = findViewById(R.id.main_live_bottom);
        rl_main_bottom = findViewById(R.id.main_commonFastLib);
        view_main_bottom = findViewById(R.id.view_main_bottom);

        tv_live_bottom_downlaod = findViewById(R.id.tv_live_bottom_downlaod);
        tv_live_bottom_uplaod = findViewById(R.id.tv_live_bottom_uplaod);
        tv_live_bottom_delete = findViewById(R.id.tv_live_bottom_delete);
        tv_live_bottom_add_video = findViewById(R.id.tv_live_bottom_add_video);
        tv_live_bottom_share = findViewById(R.id.tv_live_bottom_share);
        view_add_video = findViewById(R.id.view_add_video);
        view_bottom_share = findViewById(R.id.view_bottom_share);
        tv_live_bottom_downlaod.setOnClickListener(view -> {
            if (!ClickUtil.isFastClick()) {
                return;
            }
            if ((viewModel.getGalleryTab().getValue() == 0)) {//本地
                if (mContext == null || addAlbumDialog == null) {
                    return;
                }
                addAlbumDialog.show();
            } else if (viewModel.getGalleryTab().getValue() == 1) {//cloud
                cloudGalleryViewModel.triggerDownload();
            }
        });
        tv_live_bottom_uplaod.setOnClickListener(view -> {
            if (!ClickUtil.isFastClick()) {
                return;
            }
            if ((viewModel.getGalleryTab().getValue() == 0)) {//本地
                if (!MySetting.getInstance().getUserInfoEntity().isLogin()) {//未登录
                    logigDialog.show();
                    return;
                }
                localGalleryViewModel.uploadVideos();
            } else if (viewModel.getGalleryTab().getValue() == 1) {//cloud
                cloudGalleryViewModel.triggerShare();
            }
        });
        tv_live_bottom_delete.setOnClickListener(view -> {
            if (!ClickUtil.isFastClick()) {
                return;
            }
            if ((viewModel.getGalleryTab().getValue() == 0)) {
                if (mContext == null || deleteVideoDialog == null) {
                    return;
                }
                deleteVideoDialog.show();
            } else if (viewModel.getGalleryTab().getValue() == 1) {
                cloudGalleryViewModel.triggerDelete();
                localGalleryViewModel.setForceUpdate();
                localGalleryViewModel.checkShouldUpdateList(GalleryRvCheckState.Uncheckable);
            }
        });
        tv_live_bottom_add_video.setOnClickListener(view -> {
            if (!ClickUtil.isFastClick()) {
                return;
            }
            if (viewModel.getGalleryTab().getValue() == 0) {
                localGalleryViewModel.addVideo(mContext,
                        viewModel.getCheckState().getValue(),
                        () -> {
                            try {
                                if (mLoadingDialog != null && mLoadingDialog.isShowing()) {
                                    mLoadingDialog.dismiss();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            } finally {
                                mLoadingDialog = null;
                            }
                            Log.i(TAG, "PictureSelector Cancel");
                            return null;
                        });
            }
        });
        tv_live_bottom_share.setOnClickListener(v -> {
            if (!ClickUtil.isFastClick()) {
                return;
            }
            if (localGalleryViewModel.getVideoCheckList().getValue().size() != 1) {
                multiSelectShareDialog = new MyDialog(mContext, R.string.confirm, R.string.share_multi_video_warning, MyDialog.CONTENT_CONFIRM);
                multiSelectShareDialog.show();
                return;
            }
            if (!MySetting.getInstance().getUserInfoEntity().isLogin()) {//未登录
                logigDialog.show();
                return;
            }
            MobclickAgentUtil.clickEvent(GlobalConstant.KEY_SHARE, GlobalConstant.VALUE_SHARE_FROM_HOME);
            Intent intent = new Intent(mContext, ShareActivity.class);
            intent.putExtra("videoUrl", localGalleryViewModel.getVideoCheckList().getValue().get(0).getUrl());
            intent.putExtra("thumbnail", localGalleryViewModel.getVideoCheckList().getValue().get(0).getBase64());
            startActivity(intent);
        });

        bottomList.clear();
        bottomList.add(findViewById(R.id.ic_hottom_a));
        bottomList.add(findViewById(R.id.ic_hottom_b));
        bottomList.add(findViewById(R.id.ic_hottom_c));
        bottomList.add(findViewById(R.id.ic_hottom_d));
        imageList.clear();
        imageList.add(findViewById(R.id.ic_machine));
        imageList.add(findViewById(R.id.ic_cloud));
        imageList.add(findViewById(R.id.ic_live));
        imageList.add(findViewById(R.id.ic_my));
        textList.clear();
        textList.add(findViewById(R.id.tv_machine));
        textList.add(findViewById(R.id.tv_cloud));
        textList.add(findViewById(R.id.tv_live));
        textList.add(findViewById(R.id.tv_my));
        clickableTabViews.clear();
        clickableTabViews.add(findViewById(R.id.rl_machine));
        clickableTabViews.add(findViewById(R.id.rl_cloud));
        clickableTabViews.add(findViewById(R.id.rl_live));
        clickableTabViews.add(findViewById(R.id.rl_my));

        iv_home_video = findViewById(R.id.iv_home_video);
    }


    // 处理权限请求结果

    private void initListener() {
        iv_home_video.setOnClickListener((v) -> showBlueDialog());
        for (int i = 0; i < clickableTabViews.size(); i++) {
            int finalI = i;
            clickableTabViews.get(i).setOnClickListener(view -> mTabLayout.setCurrentTab(finalI));
        }

        mTabLayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                bottomList.get(bottomPosition).setVisibility(View.INVISIBLE);
                bottomList.get(position).setVisibility(View.VISIBLE);

                imageList.get(bottomPosition).setImageResource(imageUnSelect[bottomPosition]);
                imageList.get(position).setImageResource(imageSelect[position]);

                textList.get(bottomPosition).setVisibility(View.VISIBLE);
                textList.get(position).setVisibility(View.INVISIBLE);
                bottomPosition = position;
                if (position == 0) {
                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_MAIN_BOTTOM, GlobalConstant.VALUE_DEVICE);
                } else if (position == 1) {
                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_MAIN_BOTTOM, GlobalConstant.VALUE_LOCAL_STORAGE);
                } else if (position == 2) {
                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_MAIN_BOTTOM, GlobalConstant.VALUE_CLOUD_STORAGE);
                } else if (position == 3) {
                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_MAIN_BOTTOM, GlobalConstant.VALUE_MY);
                }

            }

            @Override
            public void onTabReselect(int position) {

            }
        });
    }

    public void checkPermission(boolean goDeviceList) {
        PermissionUtil permissionUtil = new PermissionUtil(this);
        permissionUtil.request(Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ? permissions : lowPermissions).result(aBoolean -> {
            if (aBoolean) {
                BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                //判断是否打开了蓝牙功能
                if (!bluetoothAdapter.isEnabled()) {
                    if (goDeviceList) {
                        checkBlueToothEnableForGoDeviceList();
                    } else {
                        checkBlueToothEnable();
                    }
                } else {
                    if (machineFragment != null) {
                        if (goDeviceList) {
                            machineFragment.startBleBluetTooth();
                        }
                        machineFragment.startBlueToothScan();

                    }
                }

            } else {
                Util.goIntentSetting(this, PictureConfig.REQUEST_GO_SETTING);
            }
            return null;
        });
    }


    @SuppressLint("MissingPermission")
    public void checkBlueToothMonitorEnable() {
        Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
        startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT_MONITORED);
    }


    @SuppressLint("MissingPermission")
    public void checkBlueToothEnable() {
        Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
        startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT_BONDED);
    }

    @SuppressLint("MissingPermission")
    public void checkBlueToothEnableForGoDeviceList() {
        Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
        startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
    }

    private void showBlueDialog() {
        MobclickAgentUtil.clickEvent(GlobalConstant.KEY_MAIN_BOTTOM, GlobalConstant.VALUE_RECORD);
        if (!BluetoothManager.getInstance().isGimbalConnected()) {
            ToastUtil.show(getString(R.string.no_gimble_connected));
            return;
        }
        if (viewModel.isCheckBlocking()) {
            return;
        }
        if (BluetoothManager.getInstance().isCurrentGen2() && ((Gen2Gimbal) BluetoothManager.getInstance().getCurrentConnectedGimbal()).isBlockedDevice()) {
            new MyDialog(this, R.string.device_recalled, 0, 0).show();
            return;
        }
        if (App.lowAppVersion) {
            MyDialog dialog = new MyDialog(this, R.string.app_need_update, 0, 0);
            dialog.setCallback(() -> {
                Uri uri = Uri.parse(BuildConfig.UPDATE_URL);
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(intent);
            });
            dialog.show();
            return;
        }
        if (BluetoothManager.getInstance().isCurrentGen2()) {
            if (((Gen2Gimbal) BluetoothManager.getInstance().getCurrentConnectedGimbal()).apiVersion == 4) {
                MyDialog myDialog = new MyDialog(this, R.string.firmware_update_tip, 0, 0);
                myDialog.setCallback(() -> {
                    viewModel.setFirmWareUpdateIsShowing(false);
                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_SETTINGS, GlobalConstant.VALUE_FIRMWARE_UPDATE);
                    FastUtil.startActivity(mContext, FirmWareUpdateActivity.class);
                });
                myDialog.setCancelCallback(() -> viewModel.setFirmWareUpdateIsShowing(false));
                if (!viewModel.getFirmWareUpdateIsShowing()) {
                    viewModel.setFirmWareUpdateIsShowing(true);
                    myDialog.show();
                }
                return;
            } else if (((Gen2Gimbal) BluetoothManager.getInstance().getCurrentConnectedGimbal()).apiVersion > BuildConfig.API_MAX_VERSION) {
                MyDialog myDialog = new MyDialog(this, R.string.app_need_update, 0, 0);
                myDialog.setCallback(() -> {
                    Uri uri = Uri.parse(BuildConfig.UPDATE_URL);
                    Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                    startActivity(intent);
                });
                myDialog.show();
                return;
            } else if (((Gen2Gimbal) BluetoothManager.getInstance().getCurrentConnectedGimbal()).apiVersion < BuildConfig.API_MIN_VERSION) {
                MyDialog myDialog = new MyDialog(this, R.string.firmware_update_tip, 0, 0);
                myDialog.setCallback(() -> {
                    viewModel.setFirmWareUpdateIsShowing(false);
                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_SETTINGS, GlobalConstant.VALUE_FIRMWARE_UPDATE);
                    FastUtil.startActivity(mContext, FirmWareUpdateActivity.class);
                });
                myDialog.setCancelCallback(() -> viewModel.setFirmWareUpdateIsShowing(false));
                if (!viewModel.getFirmWareUpdateIsShowing()) {
                    viewModel.setFirmWareUpdateIsShowing(true);
                    myDialog.show();
                }
                return;
            }
        }
        if (mTabLayout.getCurrentTab() == 0) {
            showSportsPop();
        } else {
            mTabLayout.setCurrentTab(0);
        }
    }


    public void showGuide() {
        GuideMask guideMask = new GuideMask.Builder(this)
                //蒙版所在activity
                .setMaskActivity(this)
                //目标布局
                .setTargetView(iv_home_video)
                //蒙版背景颜色包括透明度
                .setBgColor(Color.parseColor("#99000000"))
                //蒙版布局
                .setMaskLayout(getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE ? R.layout.layout_mask_land : R.layout.layout_mask)
                //关闭蒙版的按钮
                .setMaskCloseId(R.id.guid_bg)
                //围栏与目标布局的距离
                .setFencePadding(0, 0, 20, -20)
                //围栏布局四角的角度
                .setFenceRadius(800).build();
        guideMask.setOnDismissListener(touchTarget -> {
            SPUtil.put(mContext, SPConstant.SP_KEY_HAVE_GUIDE, true);
            if (touchTarget) {
                showBlueDialog();
            }
        }).show();
    }

    @Nullable
    @Override
    public List<FastTabEntity> getTabList() {
        ArrayList<FastTabEntity> mTabEntities = new ArrayList<>();
        machineFragment = MachineFragment.newInstance();
        galleryFragment = GalleryFragment.newInstance();
        cloudFileFragment = LiveListFragment.newInstance();
        newMainFragment = NewMainFragment.newInstance();
        myFragment = MyFragment.newInstance();
        BusinessDataProviderMgr.getInstance().registerProvider("localmedia", new ILocalMediaProvider() {
            @Override
            public List<MediaData> getLocalMedia() {

                VideoDatabaseUseCaseShare.INSTANCE.loadAllFromDB(
                        App.getVideoDatabase().videoDao(),
                        App.getVideoDatabase().folderDao(),
                        GalleryRvCheckState.Checkable,
                        videoCheckList.value,
                        )
                )
                return ;
            }

            @Override
            public String getProviderName() {
                return "localmedia";
            }

            @Override
            public String getProviderDescription() {
                return "获取本地相册";
            }
        });
        mTabEntities.add(new FastTabEntity(0, 0, machineFragment));
        mTabEntities.add(new FastTabEntity(0, 0, galleryFragment));
        mTabEntities.add(new FastTabEntity(0, 0, newMainFragment));
        mTabEntities.add(new FastTabEntity(0, 0, myFragment));
        return mTabEntities;
    }

    public void bottomGone() {
        rl_main_bottom.setVisibility(View.VISIBLE);
        view_main_bottom.setVisibility(View.VISIBLE);
        iv_home_video.setVisibility(View.VISIBLE);
        main_live_bottom.setVisibility(View.GONE);
    }

    public void bottomVisible(int current) {
        if (current == 0) {
            tv_live_bottom_downlaod.setText(R.string.save_to_album);
            Drawable drawableDownLoadImg = ContextCompat.getDrawable(this, R.drawable.selector_select_bottom_img_download);
            if (drawableDownLoadImg != null) {
                drawableDownLoadImg.setBounds(0, 0, drawableDownLoadImg.getMinimumWidth(), drawableDownLoadImg.getMinimumHeight());
            }
            tv_live_bottom_downlaod.setCompoundDrawables(null, drawableDownLoadImg, null, null);
            tv_live_bottom_uplaod.setText(R.string.upload);
            Drawable drawable = ContextCompat.getDrawable(this, R.drawable.selector_select_live_bottom_upload);
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
            }
            tv_live_bottom_uplaod.setCompoundDrawables(null, drawable, null, null);
            tv_live_bottom_add_video.setText(R.string.add_video);
            Drawable drawableAddVideo = ContextCompat.getDrawable(this, R.drawable.ic_live_bottom_addvideo);
            if (drawableAddVideo != null) {
                drawableAddVideo.setBounds(0, 0, drawableAddVideo.getMinimumWidth(), drawableAddVideo.getMinimumHeight());
            }
            tv_live_bottom_add_video.setCompoundDrawables(null, drawableAddVideo, null, null);
            tv_live_bottom_add_video.setEnabled(true);
            view_add_video.setVisibility(View.VISIBLE);
            tv_live_bottom_add_video.setVisibility(View.VISIBLE);
            tv_live_bottom_share.setVisibility(View.VISIBLE);
            view_bottom_share.setVisibility(View.VISIBLE);
        } else if (current == 1) {
            tv_live_bottom_downlaod.setText(R.string.download_local);
            Drawable drawableDownLoadVideo = ContextCompat.getDrawable(this, R.drawable.selector_select_live_bottom_download);
            if (drawableDownLoadVideo != null) {
                drawableDownLoadVideo.setBounds(0, 0, drawableDownLoadVideo.getMinimumWidth(), drawableDownLoadVideo.getMinimumHeight());
            }
            tv_live_bottom_downlaod.setCompoundDrawables(null, drawableDownLoadVideo, null, null);
            tv_live_bottom_uplaod.setText(R.string.share);
            Drawable drawableShare = ContextCompat.getDrawable(this, R.drawable.selector_select_live_bottom_share);
            if (drawableShare != null) {
                drawableShare.setBounds(0, 0, drawableShare.getMinimumWidth(), drawableShare.getMinimumHeight());
            }
            tv_live_bottom_uplaod.setCompoundDrawables(null, drawableShare, null, null);
//            tv_live_bottom_add_video.setText(R.string.more);
//            Drawable drawableMore = ContextCompat.getDrawable(this, R.drawable.ic_live_bottom_more_unclick);
//            if (drawableMore != null) {
//                drawableMore.setBounds(0, 0, drawableMore.getMinimumWidth(), drawableMore.getMinimumHeight());
//            }
//            tv_live_bottom_add_video.setCompoundDrawables(null, drawableMore, null, null);
//            tv_live_bottom_add_video.setEnabled(false);
            view_add_video.setVisibility(View.GONE);
            tv_live_bottom_add_video.setVisibility(View.GONE);
            tv_live_bottom_share.setVisibility(View.GONE);
            view_bottom_share.setVisibility(View.GONE);
        }
        rl_main_bottom.setVisibility(View.GONE);
        view_main_bottom.setVisibility(View.GONE);
        iv_home_video.setVisibility(View.GONE);
        main_live_bottom.setVisibility(View.VISIBLE);
    }

    public void liveBottomEnable(boolean enable) {
        tv_live_bottom_downlaod.setEnabled(enable);
        tv_live_bottom_uplaod.setEnabled(enable);
        tv_live_bottom_delete.setEnabled(enable);
    }

    public void liveBottomVisible(int visible) {
        tv_live_bottom_downlaod.setVisibility(visible);
        tv_live_bottom_uplaod.setVisibility(visible);
        tv_live_bottom_delete.setVisibility(visible);
    }


    @Override
    public boolean isSwipeEnable() {
        return false;
    }

    private class CustomShareListener implements UMShareListener {

        private WeakReference<MainActivity> mActivity;

        private CustomShareListener(MainActivity activity) {
            mActivity = new WeakReference(activity);
        }

        @Override
        public void onStart(SHARE_MEDIA platform) {

        }

        @Override
        public void onResult(SHARE_MEDIA platform) {

            if (platform.name().equals("WEIXIN_FAVORITE")) {
                Toast.makeText(mActivity.get(), platform + " 收藏成功啦", Toast.LENGTH_SHORT).show();
            } else {
                if (platform != SHARE_MEDIA.MORE && platform != SHARE_MEDIA.SMS && platform != SHARE_MEDIA.EMAIL && platform != SHARE_MEDIA.FLICKR && platform != SHARE_MEDIA.FOURSQUARE && platform != SHARE_MEDIA.TUMBLR && platform != SHARE_MEDIA.POCKET && platform != SHARE_MEDIA.PINTEREST

                        && platform != SHARE_MEDIA.INSTAGRAM && platform != SHARE_MEDIA.YNOTE && platform != SHARE_MEDIA.EVERNOTE) {
                    Toast.makeText(mActivity.get(), platform + " 分享成功啦", Toast.LENGTH_SHORT).show();
                }

            }
            liveBottomVisible(View.VISIBLE);
        }

        @Override
        public void onError(SHARE_MEDIA platform, Throwable t) {
            if (platform != SHARE_MEDIA.MORE && platform != SHARE_MEDIA.SMS && platform != SHARE_MEDIA.EMAIL && platform != SHARE_MEDIA.FLICKR && platform != SHARE_MEDIA.FOURSQUARE && platform != SHARE_MEDIA.TUMBLR && platform != SHARE_MEDIA.POCKET && platform != SHARE_MEDIA.PINTEREST

                    && platform != SHARE_MEDIA.INSTAGRAM && platform != SHARE_MEDIA.YNOTE && platform != SHARE_MEDIA.EVERNOTE) {


                if (Objects.requireNonNull(t.getMessage()).contains("2008")) {
                    Toast.makeText(mActivity.get(), "请安装QQ", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(mActivity.get(), platform + " 分享失败啦", Toast.LENGTH_SHORT).show();
                }
            }
            liveBottomVisible(View.VISIBLE);
        }

        @Override
        public void onCancel(SHARE_MEDIA platform) {
            MobclickAgentUtil.clickEvent(GlobalConstant.KEY_CLOUD, GlobalConstant.VALUE_SHARECANCEL);
            Toast.makeText(mActivity.get(), platform + " 分享取消了", Toast.LENGTH_SHORT).show();
            liveBottomVisible(View.VISIBLE);
        }
    }


    @Override
    public void setTabLayout(CommonTabLayout tabLayout) {

    }

    private void reportShareEvent(String uuid, String docCode) {
        Map<String, Object> extra = new HashMap<>(3);
        extra.put("docCode", docCode);
        extra.put("shareType", 0);
        ApiRepository.getInstance().reportEvent(12, uuid, "1", extra)
                .compose(bindUntilEvent(ActivityEvent.DESTROY))
                .subscribe(
                        stringBaseEntity -> {

                        },
                        throwable -> {
                            LoggerManager.e("Directplay activity", throwable.getMessage());
                        }
                );
    }

    private String getMessageCloudShareEncyptcode(List<VideoCloudItemEntity> checkVideoList, int i){
        long timeAfter30D = Instant.now().plus(30, ChronoUnit.DAYS).toEpochMilli();
        String encyptcode = "CN:" + checkVideoList.get(i).getDoc_code() + ":" + timeAfter30D;
        byte[] bytes = encyptcode.getBytes(StandardCharsets.UTF_8);
        return Base64.getEncoder().encodeToString(bytes);
    }

    public void share(List<VideoCloudItemEntity> checkVideoList) {
        String shareUUID = UUID.randomUUID().toString().replace("-", "");
        String baseUrl = BuildConfig.LIVE_SHARE_URL + "share/share.html";
        String firstUrl = "";
        StringBuilder docCode = new StringBuilder();

        StringBuilder url = new StringBuilder();
        for (int i = 0; i < checkVideoList.size(); i++) {
            String encyptcodeBase64 = getMessageCloudShareEncyptcode(checkVideoList, i);
            String shareUrl = getShareUrl(shareUUID, encyptcodeBase64, baseUrl);
            if (i == 0) {
                firstUrl = shareUrl;
                docCode = new StringBuilder(checkVideoList.get(i).getDoc_code());
            }else{
                docCode.append(",").append(checkVideoList.get(i).getDoc_code());
            }
            url.append(i + 1)
                    .append(".Start: ")
                    .append(checkVideoList.get(i).getRecordTime())
                    .append(",Duration: ")
                    .append(checkVideoList.get(i).getTime())
                    .append("\n").append(" Link: ")
                    .append(shareUrl)
                    .append("\n");
        }
        reportShareEvent(shareUUID, docCode.toString());
        /*增加自定义按钮的分享面板*/
        if (App.language.equals("zh")) {
            String finalFirstUrl = firstUrl;
            mShareAction = new ShareAction(MainActivity.this)
                    //  .addButton(getString(R.string.weixin), "wechat", "umeng_socialize_wechat", "umeng_socialize_wechat")
                    // .addButton(getString(R.string.weixin_circle), "wxcircle", "umeng_socialize_wxcircle", "umeng_socialize_wxcircle")
                    .addButton(getString(R.string.qq), "qq", "umeng_socialize_qq", "umeng_socialize_qq").addButton(getString(R.string.qzong), "qzone", "umeng_socialize_qzone", "umeng_socialize_qzone").addButton(getString(R.string.copy_link), "复制链接", "umeng_socialize_copyurl", "umeng_socialize_copyurl").setShareboardclickCallback((snsPlatform, share_media) -> {
                        liveBottomVisible(View.VISIBLE);
                        if (snsPlatform.mShowWord.equals("复制链接")) {
                            MobclickAgentUtil.clickEvent(GlobalConstant.KEY_CLOUD, GlobalConstant.VALUE_SHARELINK);
                            ClipboardManager clipboardManager = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                            ClipData mClipData = ClipData.newPlainText("url", url);
                            clipboardManager.setPrimaryClip(mClipData);
                            ToastUtil.show(mContext.getText(R.string.fast_copy_success));

                        } else {
                            SHARE_MEDIA media = SHARE_MEDIA.WEIXIN;
                            switch (snsPlatform.mKeyword) {
                                case "wechat":
                                    media = SHARE_MEDIA.WEIXIN;
                                    break;
                                case "wxcircle":

                                    media = SHARE_MEDIA.WEIXIN_CIRCLE;
                                    break;
                                case "qq":
                                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_CLOUD, GlobalConstant.VALUE_SHAREQQ);
                                    media = SHARE_MEDIA.QQ;
                                    break;
                                case "qzone":
                                    MobclickAgentUtil.clickEvent(GlobalConstant.KEY_CLOUD, GlobalConstant.VALUE_SHAREQQZONE);
                                    media = SHARE_MEDIA.QZONE;
                                    break;

                            }
//                            if (snsPlatform.mKeyword.endsWith("qq") || snsPlatform.mKeyword.endsWith("qzone")) {
//                                boolean qqClientAvailable = isQQClientAvailable(getApplicationContext());
//                                if (!qqClientAvailable) {
//                                    //Toast.makeText(MainActivity.this, "请安装QQ", Toast.LENGTH_SHORT).show();
//                                    return;
//                                }
//                            }
                            // UMWeb web = new UMWeb(checkVideoList.get(0).getUrl());
                            UMWeb web = new UMWeb(finalFirstUrl);
                            web.setTitle(getString(R.string.video_share));
                            web.setDescription(getString(R.string.share_desc, MySetting.getInstance().getUserInfoEntity().name));
//                            web.setTitle("测试");
//                            web.setDescription("测试");
                            web.setThumb(new UMImage(MainActivity.this, R.drawable.ic_launcher));
                            shareAction = new ShareAction(MainActivity.this).withMedia(web).setPlatform(media).setCallback(mShareListener);
                            if (checkVideoList.size() > 1) {
                                shareOnlyOneDialog.show();
                            } else {
                                continueShare();
                            }
                        }
                    });
        } else {
            mShareAction = new ShareAction(MainActivity.this).addButton(getString(R.string.copy_link), "复制链接", "umeng_socialize_copyurl", "umeng_socialize_copyurl")

                    .setShareboardclickCallback((snsPlatform, share_media) -> {
                        liveBottomVisible(View.VISIBLE);
                        if (snsPlatform.mShowWord.equals("Copy Link") || snsPlatform.mShowWord.equals("リンクをコピーする")) {
                            MobclickAgentUtil.clickEvent(GlobalConstant.KEY_CLOUD, GlobalConstant.VALUE_SHARELINK);
                            ClipboardManager clipboardManager = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                            ClipData mClipData = ClipData.newPlainText("url", url);
                            clipboardManager.setPrimaryClip(mClipData);

                            ToastUtil.show(mContext.getText(R.string.fast_copy_success));
                        }
                    });
        }

        ShareBoardConfig config = new ShareBoardConfig();
        config.setTitleText(getString(R.string.share_title));
        config.setCancelButtonText(getString(R.string.cancel_share));
        config.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                MobclickAgentUtil.clickEvent(GlobalConstant.KEY_CLOUD, GlobalConstant.VALUE_SHARECANCEL);
                liveBottomVisible(View.VISIBLE);
            }
        });
        mShareAction.open(config);
        liveBottomVisible(View.GONE);
    }

    private String getShareUrl(String shareUUID, String encyptcodeBase64, String baseUrl) {
        String langParam;
        switch (App.language) {
            case "en":
                langParam = "en_US";
                break;
            case "zh":
                langParam = "zh_CN";
                break;
            case "ja":
                langParam = "ja_JP";
                break;
            default:
                langParam = "en_US";
                break;
        }
        String param = "?shareEventId=" + shareUUID + "&lang=" + langParam + "&encyptcode=" + encyptcodeBase64;
        String shareUrl = baseUrl + param;
        return shareUrl;
    }

    /**
     * 判断qq是否可用
     *
     * @param context
     * @return
     */
    public static boolean isQQClientAvailable(Context context) {
        final PackageManager packageManager = context.getPackageManager();
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);
        if (pinfo != null) {
            for (int i = 0; i < pinfo.size(); i++) {
                String pn = pinfo.get(i).packageName;
                if (pn.equals("com.tencent.mobileqq")) {
                    return true;
                }
            }
        }
        return false;
    }

    private void continueShare() {
        if (shareAction != null) {
            shareAction.share();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        /** attention to this below ,must add this**/
        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_CANCELED) {
            if (requestCode == PictureConfig.REQUEST_CAMERA) {
                boolean isCheckCamera = PermissionChecker.isCheckCamera(this);
                if (isCheckCamera) {
                    goLivePushActivity();
                } else {
                    ToastUtils.showToast(mContext, getString(R.string.ps_camera));
                }
            } else if (requestCode == PictureConfig.REQUEST_GO_SETTING) {
                boolean isCheckLocation = PermissionChecker.isCheckLocation(this);
                boolean isCheckBlueTooth = true;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    isCheckBlueTooth = PermissionChecker.isCheckBlueTooth(this);
                }

                if (isCheckLocation && isCheckBlueTooth) {
                    Log.e(TAG, "去后台给了权限");
                    if (machineFragment != null) {
                        machineFragment.startBlueToothScan();
                    }
                } else {
                    if (!isCheckLocation) {
                        ToastUtils.showToast(mContext, getString(R.string.no_location));
                        return;
                    }
                    ToastUtils.showToast(mContext, getString(R.string.no_bluetooth));

                }
            } else if (requestCode == REQUEST_ENABLE_BT || requestCode == REQUEST_ENABLE_BT_MONITORED) {
                ToastUtil.show(mContext.getString(R.string.no_bluetooth_enable));
            }
        } else if (resultCode == Activity.RESULT_OK) {
            if (requestCode == REQUEST_ENABLE_BT) {
                if (machineFragment != null) {
                    machineFragment.startBlueToothScan();
                    machineFragment.startBleBluetTooth();
                }
            } else if (requestCode == REQUEST_ENABLE_BT_BONDED) {
                if (machineFragment != null) {
                    machineFragment.startBlueToothScan();
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        BluetoothManager.getInstance().destroy();
        if (subscribe != null) {
            subscribe.dispose();
            subscribe = null;
        }
        if (shareOnlyOneDialog != null) {
            shareOnlyOneDialog.destroy();
            shareOnlyOneDialog = null;
        }
        if (broadcastDialog != null) {
            broadcastDialog.destroy();
            broadcastDialog = null;
        }
        if (addAlbumDialog != null) {
            addAlbumDialog.destroy();
            addAlbumDialog = null;
        }
        if (deleteVideoDialog != null) {
            deleteVideoDialog.destroy();
            deleteVideoDialog = null;
        }
        if (logigDialog != null) {
            logigDialog.destroy();
            logigDialog = null;
        }
        if (multiSelectShareDialog != null) {
            multiSelectShareDialog.destroy();
            multiSelectShareDialog = null;
        }
        if (lowStorageDialog != null) {
            lowStorageDialog.destroy();
            lowStorageDialog = null;
        }

        if (thread != null) {
            thread.interrupt();
            try {
                thread.join(100);
            } catch (InterruptedException e) {
                thread.interrupt();
            }
            thread = null;
        }

        LogFileWriter.stopLogcat();
        UMShareAPI.get(this).release();
    }

    public void blueDisconnect() {
        if (subscribe != null) {
            subscribe.dispose();
            subscribe = null;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        EventBus.getDefault().post(new EditTaskTimerEvent(true));
    }

    @Override
    protected void onPause() {
        super.onPause();
        EventBus.getDefault().post(new EditTaskTimerEvent(false));
    }

    @Override
    protected void onStart() {
        Log.d(TAG, "onStart");
        if (subscribe != null && haveOut) {
            Log.e(TAG, "发送数据断开");
            subscribe.dispose();
            subscribe = null;
        }
        haveOut = false;
        super.onStart();
    }

    @Override
    protected void onStop() {
        haveOut = true;
        Log.d(TAG, "onStop");
        super.onStop();
    }

    private boolean haveOut = false;

    private void goLivePushActivity() {
        FastUtil.startActivity(mContext, DetectorActivity.class);
    }

    // 接入了Zendesk 之后这个onbackpress就不好使了，改成在init的callback OnbackpressDispatcher()了
//    @Override
//    public void onBackPressed() {
//        quitApp();
//    }

    public void videoHomeClickable(boolean clickable) {
        iv_home_video.setBackgroundResource(clickable ? R.drawable.ic_home_video_click : R.drawable.ic_home_video_unclick);
    }

    private void startAIControl() {
        Log.e(TAG, "重启发送数据");
        if (subscribe != null) {

            if (!subscribe.isDisposed()) {
                subscribe.dispose();
            }
            subscribe = null;
        }
        subscribe = Flowable.interval(0, 33, TimeUnit.MILLISECONDS).onBackpressureDrop()  //加上背压策略
                .subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).doOnNext(aLong -> {
                    //  Log.i(TAG, "stringFromJxr3");
                    if (DetectionControl.getInstance().getControl_style() == DetectionControl.AI_CONTROL) {
                        // Log.d(TAG, "ai识别发送数据:" + DetectionControl.getInstance().controlX());
                        BluetoothManager.getInstance().controlGimbalMovement(DetectionControl.getInstance().controlX(), DetectionControl.getInstance().controlY());

                    }
                    // Log.d(TAG, "x:" + DetectionControl.getInstance().controlX());
                }).subscribe();
    }


    private void cancelRemoter(String luid) {
        ApiRepository.getInstance().remoterCancel(luid).compose(bindUntilEvent(ActivityEvent.DESTROY)).subscribe(new FastObserver<String>() {
            @Override
            public void _onNext(String entity) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public boolean isLocationEnabled(Context context) {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    /**
     * 退出程序
     */
    protected void quitApp() {
        mQuitAppControl = FastManager.getInstance().getQuitAppControl();
        mDelayBack = mQuitAppControl != null ? mQuitAppControl.quipApp(mIsFirstBack, this) : mDelayBack;
        //时延太小或已是第二次提示直接通知执行最终操作
        if (mDelayBack <= 0 || !mIsFirstBack) {
            if (mQuitAppControl != null) {
                mQuitAppControl.quipApp(false, this);
            } else {
                MobclickAgent.onKillProcess(this);
                FastStackUtil.getInstance().exit();
            }
            return;
        }
        //编写逻辑
        if (mIsFirstBack) {
            mIsFirstBack = false;
            RxJavaManager.getInstance().setTimer(mDelayBack).compose(this.<Long>bindUntilEvent(ActivityEvent.DESTROY)).subscribe(new FastObserver<Long>() {
                @Override
                public void _onNext(Long entity) {
                    mIsFirstBack = true;
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void setEvent(StartAITimerEvent startAITimerEvent) {
        if (BluetoothManager.getInstance().isCurrentGen1()) {
            startAIControl();
        }
    }
}
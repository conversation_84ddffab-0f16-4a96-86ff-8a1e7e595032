package com.blink.blinkfocos.module.live.cameraNew.manager

import android.util.Log
import android.view.OrientationEventListener
import com.blink.blinkfocos.App
import com.blink.blinkfocos.module.live.cameraNew.constant.Orientation

class ComposeCameraRotationManager(
    val onOrientationChange: (Orientation) -> Unit,
) : OrientationEventListener(App.getContext()) {
    private var isStreaming = false
    private var isRecording = false
    private var isMonitorBroadcasting = false

    private var lastOrientation: Orientation? = null

    override fun onOrientationChanged(orientation: Int) {
        val uiRotation = when {
            orientation in 350..360 || orientation in 0..10 -> Orientation.PORTRAIT
            orientation in 80..100 -> Orientation.LANDSCAPE
            orientation in 170..190 -> Orientation.PORTRAIT
            orientation in 260..280 -> Orientation.LANDSCAPE
            else -> null
        }

        if (!isStreaming && !isRecording && !isMonitorBroadcasting) {
            uiRotation?.let {
                if (it != lastOrientation) {
                    onOrientationChange(it)
                    lastOrientation = it
                }
            }
        }
    }

    fun setStreaming(isStreaming: Boolean) {
        this.isStreaming = isStreaming
    }

    fun setRecording(isRecording: Boolean) {
        this.isRecording = isRecording
    }

    fun setMonitorBroadcasting(isMonitorBroadcasting: Boolean) {
        this.isMonitorBroadcasting = isMonitorBroadcasting
    }

    fun startListener() {
        this.enable()
    }

    fun destroy() {
        this.disable()
    }

}
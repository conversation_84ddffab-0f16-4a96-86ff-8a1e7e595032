package com.blink.blinkfocos.module.live.cameraNew.presentation

import android.app.Activity
import android.os.Build
import androidx.activity.compose.LocalActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.displayCutout
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.lifecycle.viewmodel.compose.viewModel
import com.blink.blinkfocos.base.XbotGoAppTheme
import com.blink.blinkfocos.module.live.cameraNew.constant.Orientation
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.CameraBase
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.CameraControl
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.CameraDialogComponent
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.CameraFullScreenTextComponent
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.CameraShareLivePopup
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.GuidePager
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.MonitorHintDialog
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.StartLiveDialog
import com.blink.blinkfocos.module.live.cameraNew.presentation.component.TrackingSetting
import com.blink.blinkfocos.module.live.cameraNew.util.changeOrientation
import com.blink.blinkfocos.module.live.cameraNew.viewmodel.ComposeCameraViewModel
import com.blink.blinkfocos.util.Util
import com.luck.picture.lib.config.PictureConfig
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun CameraScreen() {
    val context = LocalContext.current
    val activity = LocalActivity.current
    val window = activity?.window
    val viewmodel: ComposeCameraViewModel = viewModel()
    val scope = rememberCoroutineScope()

    val uiModel by viewmodel.cameraScreenUiState.collectAsState()
    val screenOrientation by viewmodel.screenOrientation
    val shouldPopActivity by viewmodel.shouldPopActivity.observeAsState()
    val triggerChangeOrientation by viewmodel.triggerChangeOrientation.observeAsState()
    val triggerDimScreen by viewmodel.dimScreen.observeAsState()
    val triggerCheckPermission by viewmodel.requestVideoermission.observeAsState()
    val showLivePopup by remember(uiModel) {
        derivedStateOf { uiModel.liveDialogStack.isNotEmpty() }
    }

    val shouldShowSetting by remember(uiModel) {
        derivedStateOf { uiModel.isSettingOpen }
    }

    val videoPermissionLauncher =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.RequestMultiplePermissions()) { permission ->
            val allGranted = permission.all { it.value }
            if (allGranted) {
                viewmodel.startLogging()
                scope.launch {
                    delay(1000)
                    viewmodel.requestStartRecord()
                }
            } else {
                Util.goIntentSetting(activity, PictureConfig.REQUEST_GO_SETTING)
            }
        }

    val isPortrait = screenOrientation == Orientation.PORTRAIT
    // 在第一次进入页面的时候先获取当前的屏幕方向然后给Viewmodel中的Livedata赋值，
    // TODO：或许等Viewmodel中写了OrientationManager 可以直接在viewmodel中init的时候
    //  就采用OrientationManager的值？就不需在这里做赋值了\@.@/?

    LaunchedEffect(shouldPopActivity) {
        shouldPopActivity?.getContentIfNotHandled()?.let {
            (context as Activity).finish()
        }
    }

    LaunchedEffect(triggerChangeOrientation) {
        triggerChangeOrientation?.getContentIfNotHandled()?.let {
            activity?.let { activity ->
                changeOrientation(activity, it)
                viewmodel.setOrientationUiMode(it)
            }
        }
    }

    LaunchedEffect(triggerDimScreen) {
        triggerDimScreen?.getContentIfNotHandled()?.let {
            val localLayoutParams = window?.attributes
            localLayoutParams?.screenBrightness = (it)
            window?.attributes = localLayoutParams
        }
    }

    LaunchedEffect(triggerCheckPermission) {
        triggerCheckPermission?.getContentIfNotHandled()?.let {
            videoPermissionLauncher.launch(it)
        }
    }

    XbotGoAppTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFF000000))
                .windowInsetsPadding(WindowInsets.displayCutout)
        ) {
            if (isPortrait) {
                Column(Modifier.fillMaxSize()) {
                    Spacer(Modifier.height(48.dp))
                    CameraBase()
                }
            } else {
                Row(Modifier.fillMaxSize()) {
                    Spacer(Modifier.width(48.dp))
                    CameraBase()
                }
            }
        }
        if (shouldShowSetting)
            TrackingSetting()
    }
    if (showLivePopup)
        Popup(
            alignment = if (isPortrait) Alignment.BottomCenter else Alignment.CenterEnd,
            offset = with(LocalDensity.current) {
                if (isPortrait) {
                    IntOffset(0, -50.dp.toPx().toInt())
                } else {
                    IntOffset(-50.dp.toPx().toInt(), 0)
                }
            },
            properties = PopupProperties(
                focusable = true,
                dismissOnBackPress = true,
                dismissOnClickOutside = false
            ),
            onDismissRequest = { viewmodel.setLiveDialogStateBack() }
        ) {
            XbotGoAppTheme {
                StartLiveDialog()
            }
        }
    CameraDialogComponent()
    viewmodel.dialogManager.DialogHost()
    MonitorHintDialog()
    CameraFullScreenTextComponent()
    if (uiModel.showShareDialog) {
        CameraShareLivePopup()
    }
    XbotGoAppTheme {
        GuidePager()
    }
}
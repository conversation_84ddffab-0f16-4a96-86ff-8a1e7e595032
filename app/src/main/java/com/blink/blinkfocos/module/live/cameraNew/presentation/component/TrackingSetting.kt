package com.blink.blinkfocos.module.live.cameraNew.presentation.component

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.displayCutout
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Popup
import androidx.lifecycle.viewmodel.compose.viewModel
import com.aries.library.fast.util.ToastUtil
import com.blink.blinkfocos.R
import com.blink.blinkfocos.base.XbotGoAppTheme
import com.blink.blinkfocos.uikit.dialog.iOSStyleTwoButtonDialog
import com.blink.blinkfocos.module.live.Bitrate
import com.blink.blinkfocos.module.live.cameraNew.constant.Orientation
import com.blink.blinkfocos.module.live.cameraNew.constant.RecordState
import com.blink.blinkfocos.module.live.cameraNew.model.TrackingSettingState
import com.blink.blinkfocos.module.live.cameraNew.viewmodel.ComposeCameraViewModel
import com.blink.blinkfocos.module.live.chameleonBitrate
import com.blink.blinkfocos.module.live.getEntity
import com.blink.blinkfocos.module.live.gimbalBitrate
import com.blink.blinkfocos.module.live.is4k
import com.blink.blinkfocos.module.live.is60Fps
import com.blink.blinkfocos.singleton.bluetooth.BluetoothManager
import com.blink.blinkfocos.widget.SectorView
import com.blink.blinkfocos.widget.component.XbotGoClickable

@Composable
fun TrackingSetting() {
    val viewModel: ComposeCameraViewModel = viewModel()
    val uiState by viewModel.cameraScreenUiState.collectAsState()
    val isSettingOpen = uiState.isSettingOpen
    val screenOrientation by viewModel.screenOrientation

    var trackingState by remember { mutableStateOf(viewModel.trackingState.value) }

    LaunchedEffect(isSettingOpen) {
        if (isSettingOpen) {
            trackingState = viewModel.trackingState.value
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        Header {
            viewModel.dialogManager.showDialog(
                alignment = Alignment.Center,
            ) {
                iOSStyleTwoButtonDialog(
                    content = {
                        Box(Modifier.fillMaxWidth()) {
                            Text(
                                stringResource(R.string.restore_default_setting),
                                color = Color.Black,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                modifier = Modifier.align(Alignment.Center)
                            )
                        }
                    },
                    leftButton = {
                        XbotGoClickable(
                            onTap = { viewModel.dialogManager.dismissDialog() },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(41.dp)
                        ) {
                            Text(
                                stringResource(R.string.cancel),
                                fontSize = 16.sp,
                                color = colorResource(R.color.dialog_confirm)
                            )
                        }
                    },
                    rightButton = {
                        XbotGoClickable(
                            onTap = {
                                if (uiState.isStreaming || uiState.recordState != RecordState.IDLE) {
                                    trackingState =
                                        TrackingSettingState().copy(sizeSelect = trackingState.sizeSelect)
                                } else {
                                    trackingState = TrackingSettingState()
                                }
                                viewModel.dialogManager.dismissDialog()
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(41.dp)
                        ) {
                            Text(
                                stringResource(R.string.Ok),
                                fontSize = 16.sp,
                                color = colorResource(R.color.dialog_confirm)
                            )
                        }
                    }
                )
            }
        }
        if (screenOrientation == Orientation.PORTRAIT) {
            PortraitLayout(
                trackingState = trackingState,
                canChangeRes = uiState.recordState == RecordState.IDLE && !uiState.isStreaming,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 15.dp)
                    .verticalScroll(state = rememberScrollState())
            ) {
                trackingState = it
            }
        } else {
            HorizontalLayout(
                trackingState = trackingState,
                canChangeRes = uiState.recordState == RecordState.IDLE && !uiState.isStreaming,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 80.dp)
                    .verticalScroll(state = rememberScrollState())
            ) {
                trackingState = it
            }
        }
    }
}

@Composable
private fun HorizontalLayout(
    canChangeRes: Boolean,
    trackingState: TrackingSettingState,
    modifier: Modifier,
    setTrackingState: (TrackingSettingState) -> Unit,
) {
    val context = LocalContext.current

    val viewModel: ComposeCameraViewModel = viewModel()
    val updatedTrackingState by rememberUpdatedState(trackingState)

    Column(
        modifier = modifier
    ) {
        if (viewModel.getShowFullTrackingSetting()) {
            NormalSettingRow(
                stringResource(R.string.power_saving),
                if (updatedTrackingState.powerSave) stringResource(R.string.open) else stringResource(
                    R.string.close
                ),
                updatedTrackingState.powerSave,
                {
                    setTrackingState(updatedTrackingState.copy(powerSave = !updatedTrackingState.powerSave))
                },
            )
            Divider()
            NormalSettingRow(
                stringResource(R.string.timestamp_switcher),
                if (updatedTrackingState.showTimeWaterMark) stringResource(R.string.open) else stringResource(
                    R.string.close
                ),
                updatedTrackingState.showTimeWaterMark,
                {
                    setTrackingState(
                        updatedTrackingState.copy(showTimeWaterMark = !updatedTrackingState.showTimeWaterMark)
                    )
                },
            )
            Divider()
            NormalSettingRow(
                stringResource(R.string.camera_record_sound),
                if (updatedTrackingState.recordSound) stringResource(R.string.open) else stringResource(
                    R.string.close
                ),
                updatedTrackingState.recordSound,
                { setTrackingState(updatedTrackingState.copy(recordSound = !updatedTrackingState.recordSound)) },
            )
            BigDivider()
            VideoResRow(
                currentBitrate = updatedTrackingState.sizeSelect,
                canChangeRes = canChangeRes,
            ) {
                setTrackingState(updatedTrackingState.copy(sizeSelect = it))
            }
            BigDivider()
            Row {
                NormalSettingRow(
                    stringResource(R.string.auto_zoom),
                    if (updatedTrackingState.autoZoom) stringResource(R.string.open) else stringResource(
                        R.string.close
                    ),
                    updatedTrackingState.autoZoom,
                    { setTrackingState(updatedTrackingState.copy(autoZoom = !updatedTrackingState.autoZoom)) },
                )
                Spacer(modifier = Modifier.width(120.dp))
                NormalSettingRow(
                    stringResource(R.string.auto_tracking),
                    if (updatedTrackingState.autoTracking) stringResource(R.string.open) else stringResource(
                        R.string.close
                    ),
                    updatedTrackingState.autoTracking,
                    {
                        setTrackingState(
                            updatedTrackingState.copy(autoTracking = !updatedTrackingState.autoTracking)
                        )
                    },
                )
            }
            Divider()
            MachineAngle(
                updatedTrackingState.maxYaw
            ) {
                setTrackingState(
                    updatedTrackingState.copy(maxYaw = it)
                )
            }
            Divider()
            TrackingSpeed(updatedTrackingState.maxSpan) {
                setTrackingState(
                    updatedTrackingState.copy(maxSpan = it)
                )
            }
        } else {
            VideoResRow(
                currentBitrate = updatedTrackingState.sizeSelect,
                canChangeRes = canChangeRes,
            ) {
                setTrackingState(updatedTrackingState.copy(sizeSelect = it))
            }
            BigDivider()
            NormalSettingRow(
                stringResource(R.string.camera_record_sound),
                if (updatedTrackingState.recordSound) stringResource(R.string.open) else stringResource(
                    R.string.close
                ),
                updatedTrackingState.recordSound,
                { setTrackingState(updatedTrackingState.copy(recordSound = !updatedTrackingState.recordSound)) },
            )
            BigDivider()
            NormalSettingRow(
                stringResource(R.string.auto_zoom),
                if (updatedTrackingState.autoZoom) stringResource(R.string.open) else stringResource(
                    R.string.close
                ),
                updatedTrackingState.autoZoom,
                { setTrackingState(updatedTrackingState.copy(autoZoom = !updatedTrackingState.autoZoom)) },
            )
        }
    }
    Footer {
        viewModel.saveTrackingState(updatedTrackingState)
        viewModel.toggleSetting(false)
        ToastUtil.show(context.getString(R.string.setting_saved))
    }
}

@Composable
private fun PortraitLayout(
    canChangeRes: Boolean,
    trackingState: TrackingSettingState,
    modifier: Modifier,
    setTrackingState: (TrackingSettingState) -> Unit,
) {
    val context = LocalContext.current

    val viewModel: ComposeCameraViewModel = viewModel()
    val updatedTrackingState by rememberUpdatedState(trackingState)

    Column(
        modifier = modifier
    ) {
        VideoResRowPortrait(
            currentBitrate = updatedTrackingState.sizeSelect,
            canChangeRes = canChangeRes
        ) {
            setTrackingState(updatedTrackingState.copy(sizeSelect = it))
        }
        Divider()
        NormalSettingRowPortrait(
            stringResource(R.string.camera_record_sound),
            if (updatedTrackingState.recordSound) stringResource(R.string.open) else stringResource(
                R.string.close
            ),
            updatedTrackingState.recordSound,
            { setTrackingState(updatedTrackingState.copy(recordSound = !updatedTrackingState.recordSound)) },
        )
        Divider()
        NormalSettingRowPortrait(
            stringResource(R.string.auto_zoom),
            if (updatedTrackingState.autoZoom) stringResource(R.string.open) else stringResource(
                R.string.close
            ),
            updatedTrackingState.autoZoom,
            { setTrackingState(updatedTrackingState.copy(autoZoom = !updatedTrackingState.autoZoom)) },
        )
    }
    FooterPortrait {
        viewModel.saveTrackingState(updatedTrackingState)
        viewModel.toggleSetting(false)
        ToastUtil.show(context.getString(R.string.setting_saved))
    }
}

@Composable
private fun Header(onTap: () -> Unit) {
    val viewModel: ComposeCameraViewModel = viewModel()
    val screenOrientation by viewModel.screenOrientation
    val cutoutPadding = WindowInsets.displayCutout.asPaddingValues().calculateTopPadding()
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (screenOrientation == Orientation.PORTRAIT)
                    Modifier.height(44.dp + cutoutPadding)
                else
                    Modifier.height(44.dp)
            )
            .background(
                brush = Brush.linearGradient(
                    0.0f to Color(0xFFBE140E),
                    1.0f to Color(0xFFFC7D00),
                )
            ),
        contentAlignment = Alignment.BottomCenter,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
        ) {
            XbotGoClickable(onTap = {
                viewModel.toggleSetting(false)
            }) {
                Row {
                    Spacer(modifier = Modifier.width(16.dp))
                    Image(
                        painterResource(R.drawable.ic_black_left),
                        null,
                        colorFilter = ColorFilter.tint(Color.White)
                    )
                }
            }
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                stringResource(R.string.camera_setting),
                color = Color.White,
                fontSize = 16.sp,
                lineHeight = 15.sp,
                maxLines = 2,
                modifier = Modifier
                    .align(Alignment.CenterVertically)
                    .then(if (screenOrientation == Orientation.PORTRAIT) Modifier.width(110.dp) else Modifier)
            )
            Spacer(modifier = Modifier.weight(1f))
            XbotGoClickable(
                onTap = onTap, modifier = Modifier.align(Alignment.CenterVertically)
            ) {
                Text(
                    stringResource(R.string.restore_default_setting),
                    color = Color.White,
                    fontSize = 16.sp,
                    lineHeight = 15.sp,
                    maxLines = 2,
                    modifier = Modifier
                        .align(Alignment.CenterVertically)
                        .then(if (screenOrientation == Orientation.PORTRAIT) Modifier.width(115.dp) else Modifier)
                )
            }
            Spacer(modifier = Modifier.width(29.dp))
        }
    }
}

@Composable
private fun Footer(
    onTap: () -> Unit,
) {
    Box(
        Modifier
            .fillMaxWidth()
            .height(74.dp)
            .shadow(elevation = 12.dp)
            .background(Color.White)
    ) {
        XbotGoClickable(
            onTap = onTap,
            modifier = Modifier
                .size(227.dp, 48.dp)
                .padding(end = 25.dp)
                .align(Alignment.CenterEnd)
                .shadow(elevation = 12.dp, shape = RoundedCornerShape(24.dp))
                .background(
                    Brush.linearGradient(
                        0f to Color(0xFFBE140E),
                        1f to Color(0xFFFd7F00),
                    ), RoundedCornerShape(24.dp)
                )
        ) {
            Text(
                stringResource(R.string.save_current_setting),
                fontSize = 15.sp,
                color = Color.White,
                modifier = Modifier.align(alignment = Alignment.Center)
            )
        }
    }
}

@Composable
private fun FooterPortrait(
    onTap: () -> Unit,
) {
    Box(
        Modifier
            .fillMaxWidth()
            .height(74.dp)
            .shadow(elevation = 12.dp)
            .background(Color.White)
    ) {
        XbotGoClickable(
            onTap = onTap,
            modifier = Modifier
                .height(height = 52.dp)
                .fillMaxWidth()
                .padding(horizontal = 32.dp)
                .align(Alignment.CenterEnd)
                .shadow(elevation = 12.dp, shape = RoundedCornerShape(24.dp))
                .background(
                    Brush.linearGradient(
                        0f to Color(0xFFBE140E),
                        1f to Color(0xFFFd7F00),
                    ), RoundedCornerShape(24.dp)
                )
        ) {
            Text(
                stringResource(R.string.save_current_setting),
                fontSize = 15.sp,
                color = Color.White,
                modifier = Modifier.align(alignment = Alignment.Center)
            )
        }
    }
}

@Composable
private fun NormalSettingRow(
    title: String,
    buttonText: String,
    buttonSelect: Boolean,
    onTap: () -> Unit,
) {
    Row(
        modifier = Modifier.height(60.dp)
    ) {
        Text(
            title,
            color = Color(0xFF4D4D4D),
            fontSize = 14.sp,
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .width(126.dp)
        )
        Spacer(modifier = Modifier.widthIn(max = 40.dp))
        XbotGoClickable(
            onTap = onTap, modifier = Modifier.align(Alignment.CenterVertically)
        ) {
            TrackingButton(buttonSelect, buttonText)
        }
    }
}

@Composable
private fun NormalSettingRowPortrait(
    title: String,
    buttonText: String,
    buttonSelect: Boolean,
    onTap: () -> Unit,
) {
    Row(
        modifier = Modifier.height(60.dp)
    ) {
        Text(
            title,
            color = Color(0xFF4D4D4D),
            fontSize = 14.sp,
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .width(203.dp)
        )
        Spacer(modifier = Modifier.widthIn(max = 147.dp))
        XbotGoClickable(
            onTap = onTap, modifier = Modifier.align(Alignment.CenterVertically)
        ) {
            TrackingButton(buttonSelect, buttonText)
        }
    }
}

@Composable
private fun AutoSaveRow() {
    Row(
        modifier = Modifier.height(76.dp)
    ) {
        Text(
            stringResource(R.string.file_save_time),
            color = Color(0xFF4D4D4D),
            fontSize = 14.sp,
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .width(126.dp)
        )
        Spacer(modifier = Modifier.widthIn(max = 40.dp))
        Box(modifier = Modifier.align(Alignment.CenterVertically)) {
            TrackingButton(true, stringResource(R.string.thirty_minute))
        }
        Spacer(modifier = Modifier.width(10.dp))
        Box(modifier = Modifier.align(Alignment.CenterVertically)) {
            TrackingButton(false, stringResource(R.string.fifty_minute))
        }
        Spacer(modifier = Modifier.width(10.dp))
        Box(
            modifier = Modifier.align(Alignment.CenterVertically)
        ) {
            TrackingTimeTextField()
        }
        Spacer(modifier = Modifier.width(6.dp))
        Text(
            stringResource(R.string.min),
            fontSize = 14.sp,
            color = Color(0xFF4D4D4D),
            modifier = Modifier.align(Alignment.CenterVertically)
        )
    }
}

@Composable
private fun TrackingButton(
    isSelected: Boolean, text: String
) {
    Box(
        modifier = Modifier
            .size(160.dp, 30.dp)
            .background(
                if (isSelected) Color(0xFFDC4707) else Color(0xFFEFEFEF), RoundedCornerShape(75.dp)
            )
    ) {
        Text(
            text,
            fontSize = 14.sp,
            color = if (isSelected) Color.White else Color(0xFF4D4D4D),
            modifier = Modifier.align(Alignment.Center),
        )
    }
}

@Composable
private fun VideoResRow(
    canChangeRes: Boolean,
    currentBitrate: Bitrate,
    onBitrateChange: (Bitrate) -> Unit,
) {
    val context = LocalContext.current
    Row(
        modifier = Modifier.height(60.dp)
    ) {
        Text(
            stringResource(R.string.definition),
            color = Color(0xFF4D4D4D),
            fontSize = 14.sp,
            modifier = Modifier
                .align(Alignment.CenterVertically)
                .width(126.dp)
        )
        Spacer(modifier = Modifier.widthIn(max = 40.dp))
        Box(modifier = Modifier.align(Alignment.CenterVertically)) {
            DropDownMenu(
                currentBitrate = currentBitrate,
                onBitrateChange = onBitrateChange,
                isPortrait = false,
                enable = canChangeRes,
            )
        }
        Spacer(modifier = Modifier.width(15.dp))
        Box(
            Modifier
                .height(29.dp)
                .align(Alignment.CenterVertically)
                .background(Color(0xFFFFF5EF), shape = RoundedCornerShape(75.dp))
        ) {
            Row(modifier = Modifier.align(Alignment.Center)) {
                Spacer(Modifier.width(28.dp))
                Image(
                    painterResource(R.drawable.icon_warn),
                    null,
                )
                Spacer(Modifier.width(8.dp))
                Text(
                    "${currentBitrate.getEntity(context).bitratePerMin}/${stringResource(R.string.bitrate_pre_min)}",
                    fontSize = 12.sp,
                    color = Color(0xFFF28D6E),
                )
                Spacer(Modifier.width(28.dp))
            }
        }
    }
}

@Composable
private fun VideoResRowPortrait(
    canChangeRes: Boolean,
    currentBitrate: Bitrate,
    onBitrateChange: (Bitrate) -> Unit,
) {
    val context = LocalContext.current
    Column {
        Spacer(modifier = Modifier.height(15.dp))
        Text(
            stringResource(R.string.definition),
            color = Color(0xFF4D4D4D),
            fontSize = 14.sp,
            modifier = Modifier
                .align(Alignment.Start)
        )
        Spacer(modifier = Modifier.height(10.dp))
        Row {
            Spacer(modifier = Modifier.widthIn(max = 40.dp))
            Box(modifier = Modifier.align(Alignment.CenterVertically)) {
                DropDownMenu(
                    currentBitrate = currentBitrate,
                    onBitrateChange = onBitrateChange,
                    isPortrait = true,
                    enable = canChangeRes
                )
            }
            Spacer(modifier = Modifier.weight(1f))
            Box(
                Modifier
                    .size(160.dp, 29.dp)
                    .align(Alignment.CenterVertically)
                    .background(Color(0xFFFFF5EF), shape = RoundedCornerShape(75.dp))
            ) {
                Row(modifier = Modifier.align(Alignment.Center)) {
                    Spacer(Modifier.width(10.dp))
                    Image(
                        painterResource(R.drawable.icon_warn),
                        null,
                    )
                    Spacer(Modifier.width(8.dp))
                    Text(
                        "${currentBitrate.getEntity(context).bitratePerMin}/${stringResource(R.string.bitrate_pre_min)}",
                        fontSize = 12.sp,
                        color = Color(0xFFF28D6E),
                    )
                    Spacer(Modifier.width(10.dp))
                }
            }
            Spacer(modifier = Modifier.height(10.dp))
        }
        Spacer(modifier = Modifier.height(10.dp))

    }
}

@Composable
private fun DropDownMenu(
    currentBitrate: Bitrate,
    onBitrateChange: (Bitrate) -> Unit,
    isPortrait: Boolean,
    enable: Boolean,
) {
    val context = LocalContext.current
    val viewmodel: ComposeCameraViewModel = viewModel()
    var isOpen by remember { mutableStateOf(false) }
    var dropdownOffsetY by remember { mutableFloatStateOf(0f) }
    val bitrateList =
        if (BluetoothManager.getInstance().isCurrentGen1) gimbalBitrate else chameleonBitrate
    XbotGoClickable(onTap = {
        if (!enable) {
            ToastUtil.show(context.getText(R.string.no_switch_on_recording))
            return@XbotGoClickable
        }
        isOpen = !isOpen
    }) {
        Box(
            Modifier
                .size(if (isPortrait) 171.dp else 218.dp, 30.dp)
                .graphicsLayer(clip = false) // 允许子元素溢出
                .background(Color(0xFFEFEFEF), shape = RoundedCornerShape(75.dp))
                .border(1.dp, Color(0x33000000), RoundedCornerShape(75.dp))
                .onGloballyPositioned { coordinates ->
                    // 获取触发组件在父级中的相对位置
                    val position = coordinates.positionInParent()
                    dropdownOffsetY = position.y + coordinates.size.height
                }) {
            Text(
                currentBitrate.getEntity(context).displayName,
                fontSize = 14.sp,
                color = Color(0xFF4D4D4D).copy(alpha = if (enable) 1.0f else 0.5f),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 16.dp, end = 28.dp)
            )
            if (enable)
                Image(
                    painter = painterResource(R.drawable.vector_gray_inverted_triangle),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = 16.dp)
                )
        }
    }
    if (isOpen) Popup(
        alignment = Alignment.TopStart,
        offset = IntOffset(x = 0, y = with(LocalDensity.current) {
            dropdownOffsetY.toInt() + 8.dp.toPx().toInt()
        }),
        onDismissRequest = { isOpen = false }) {
        XbotGoAppTheme {
            Box(
                modifier = Modifier
                    .graphicsLayer {
                        shadowElevation = 8f
                        shape = RoundedCornerShape(12.dp)
                        clip = false
                    }
                    .size(218.dp, 167.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White, RoundedCornerShape(12.dp))) {
                LazyColumn(
                    contentPadding = PaddingValues(vertical = 10.dp, horizontal = 16.dp)
                ) {
                    items(bitrateList.size) {
                        XbotGoClickable(
                            onTap = {
                                isOpen = false
                                checkShouldShowResDialog(viewmodel, bitrateList[it]) {
                                    onBitrateChange(bitrateList[it])
                                }
                            }
                        ) {
                            Row {
                                Text(
                                    bitrateList[it].getEntity(context).displayName,
                                    color = Color(0xFF4D4D4D),
                                    modifier = Modifier.padding(vertical = 9.dp)
                                )
                                Spacer(Modifier.weight(1f))
                                if (bitrateList[it] == currentBitrate) Image(
                                    painterResource(R.drawable.red_tick),
                                    null,
                                    colorFilter = ColorFilter.tint(Color(0xFFDC4707)),
                                    modifier = Modifier.align(Alignment.CenterVertically),
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}


@Composable
private fun TrackingTimeTextField() {
    var text by remember { mutableStateOf("") }
    Box {
        OutlinedTextField(
            text,
            onValueChange = {
                if (it.all { it.isDigit() }) {
                    text = it
                }
            },
            shape = RoundedCornerShape(75.dp),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFFDC4707),
                unfocusedBorderColor = Color(0xFFEFEFEF),
                unfocusedTextColor = Color(0xFF4D4D4D),
                focusedTextColor = Color(0xFF4D4D4D),
            ),
            placeholder = {
                Text(
                    stringResource(R.string.other_duration),
                    color = Color(0xFF919191),
                    fontSize = 14.sp,
                )
            },
            modifier = Modifier
                .size(160.dp, 30.dp)
                .padding(top = 0.dp)
        )
        Text(
            text.ifBlank { stringResource(R.string.other_duration) },
            color = if (text.isBlank()) Color(0xFF919191) else Color(0xFF4D4D4D),
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MachineAngle(
    angle: Int,
    onAngleChange: (Int) -> Unit
) {
    Box(
        Modifier
            .height(210.dp)
            .fillMaxWidth(),
    ) {
        Text(
            stringResource(R.string.machine_angle),
            color = Color(0xff4d4d4d),
            modifier = Modifier.padding(top = 28.dp)
        )
        AndroidView(
            factory = {
                SectorView(it).apply {
                    startAnimated()
                    setProgress(angle.toFloat())
                }
            }, update = {
                it.startAnimated()
                it.setProgress(angle.toFloat())
            }, modifier = Modifier
                .align(Alignment.TopCenter)
                .size(135.dp)
                .padding(top = 23.dp)
        )
        Row(
            modifier = Modifier.padding(start = 540.dp, top = 52.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(10.dp)
                    .background(Color(0xfffcac8a), shape = RoundedCornerShape(2.dp))
                    .align(Alignment.CenterVertically)
            )
            Spacer(Modifier.width(2.5.dp))
            Text(
                stringResource(R.string.machine_angle), fontSize = 12.sp
            )
        }

        Row(
            modifier = Modifier.padding(start = 540.dp, top = 77.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(10.dp)
                    .background(Color(0xfffff08f), shape = RoundedCornerShape(2.dp))
                    .align(Alignment.CenterVertically)
            )
            Spacer(Modifier.width(2.5.dp))
            Text(
                stringResource(R.string.video_capture_angle), fontSize = 12.sp
            )
        }

        Row(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = 130.dp)
        ) {
            Text(
                "60°",
                fontSize = 14.sp,
                color = Color(0xFF4D4D4D),
                modifier = Modifier.align(Alignment.CenterVertically)
            )
            Slider(
                value = angle.toFloat(), onValueChange = {
                    onAngleChange(it.toInt())
                }, valueRange = 60f..150f, thumb = {
                    Image(
                        painterResource(R.drawable.ic_seekbar_thumb),
                        null,
                        modifier = Modifier.size(32.dp)
                    )
                }, track = {
                    SliderDefaults.Track(
                        it,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color(0xFFDC4707),
                            inactiveTrackColor = Color(0xFFBFBFBF)
                        ),
                        thumbTrackGapSize = 0.dp,
                        drawStopIndicator = {},
                        modifier = Modifier.height(4.dp)
                    )
                }, modifier = Modifier
                    .width(310.dp)
            )
            Text(
                "150°",
                fontSize = 14.sp,
                color = Color(0xFF4D4D4D),
                modifier = Modifier.align(Alignment.CenterVertically)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TrackingSpeed(
    speed: Int,
    onSpeedChange: (Int) -> Unit,
) {

    Box(
        Modifier
            .height(200.dp)
            .fillMaxWidth()
    ) {
        Text(
            stringResource(R.string.speed),
            color = Color(0xff4d4d4d),
            modifier = Modifier.padding(top = 28.dp)
        )
        Row(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = 117.dp)
        ) {
            Text(
                if (BluetoothManager.getInstance().isCurrentGen2) "200" else "500",
                color = colorResource(R.color.tv_black),
                fontSize = 12.sp,
                modifier = Modifier
                    .align(Alignment.Bottom)
                    .offset(y = -15.dp)
            )
            Slider(
                value = speed.toFloat(),
                onValueChange = {
                    onSpeedChange(it.toInt())
                },
                valueRange = if (BluetoothManager.getInstance().isCurrentGen2) (200f..1000f) else (500f..2500f),
                thumb = {
                    Box {
                        Image(
                            painterResource(R.drawable.ic_seekbar_thumb),
                            null,
                            modifier = Modifier.size(32.dp)
                        )
                        Image(
                            painterResource(R.drawable.shape_blue_inverted_triangle),
                            null,
                            modifier = Modifier
                                .offset(0.dp, (-12).dp)
                                .align(Alignment.TopCenter)
                        )
                        Text(
                            speed.toString(),
                            fontSize = 14.sp,
                            modifier = Modifier
                                .offset(0.dp, (-30).dp)
                                .align(Alignment.TopCenter),
                        )
                    }
                },
                track = {
                    SliderDefaults.Track(
                        it,
                        colors = SliderDefaults.colors(
                            activeTrackColor = Color.Transparent,
                            inactiveTrackColor = Color.Transparent,
                        ),
                        thumbTrackGapSize = 0.dp,
                        drawStopIndicator = {},
                        modifier = Modifier
                            .height(4.dp)
                            .background(
                                Brush.linearGradient(
                                    0f to Color(0xFFFE7F00),
                                    1f to Color(0xFF00FEE1),
                                ), RoundedCornerShape(2.dp)
                            )
                    )
                },
                modifier = Modifier
                    .width(310.dp)

            )
            Text(
                if (BluetoothManager.getInstance().isCurrentGen2) "1000" else "2500",
                color = colorResource(R.color.tv_black),
                fontSize = 12.sp,
                modifier = Modifier
                    .align(Alignment.Bottom)
                    .offset(y = -15.dp)
            )
        }
    }
}

@Composable
private fun Divider() {
    Text(
        "--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------",
        color = Color(0xFF969696),
        maxLines = 1,
    )
}

@Composable
private fun BigDivider() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(3.5.dp)
            .background(Color(0xFFD3D3D3))
    )
}

private fun checkShouldShowResDialog(
    viewmodel: ComposeCameraViewModel,
    bitrate: Bitrate,
    applyRes: () -> Unit
) {
    if (bitrate.is4k() || bitrate.is60Fps()) {
        viewmodel.dialogManager.showDialog(
            alignment = Alignment.Center
        ) {
            val context = LocalContext.current
            var alerts = arrayOf<String>()
            if (bitrate.is4k()) {
                alerts += context.getString(
                    R.string.bitrate_dialog_text,
                    bitrate.getEntity(context).bitratePerMin
                )
            }
            if (bitrate.is60Fps()) {
                alerts += stringResource(R.string.bitrate_dialog_fps_text)
            }

            iOSStyleTwoButtonDialog(
                content = {
                    Column(Modifier.fillMaxWidth()) {
                        Text(
                            alerts.get(0),
                            color = Color.Black,
                            fontSize = 16.sp,
                            textAlign = TextAlign.Center
                        )
                        if (alerts.size > 1) {
                            Spacer(Modifier.height(5.dp))
                            HorizontalDivider()
                            Spacer(Modifier.height(5.dp))
                            Text(
                                alerts.get(1),
                                color = Color.Black,
                                fontSize = 16.sp,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                },
                leftButton = {
                    XbotGoClickable(
                        onTap = {
                            viewmodel.dialogManager.dismissDialog()
                        },
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(41.dp)
                    ) {
                        Text(
                            stringResource(R.string.cancel),
                            fontSize = 16.sp,
                            color = colorResource(R.color.dialog_confirm)
                        )
                    }
                },
                rightButton = {
                    XbotGoClickable(
                        onTap = {
                            viewmodel.dialogManager.dismissDialog()
                            applyRes()
                        },
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(41.dp)
                    ) {
                        Text(
                            stringResource(R.string.Ok),
                            fontSize = 16.sp,
                            color = colorResource(R.color.dialog_confirm)
                        )
                    }
                },
            )
        }
    } else {
        applyRes()
    }
}
package com.blink.blinkfocos.singleton.bluetooth.gen1;

import android.bluetooth.BluetoothGatt;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;

import com.blink.blinkfocos.singleton.bluetooth.base.AbstractBluetoothDevice;
import com.blink.blinkfocos.singleton.bluetooth.base.AbstractConnectionStrategy;
import com.blink.blinkfocos.singleton.bluetooth.base.interfaces.ConnectionStrategy;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;


/**
 * gen1云台策略类，连接，成功后的心跳，订阅，发送数据，断开连接逻辑
 */
public class Gen1GimbalStrategy extends AbstractConnectionStrategy {
    private static final String TAG = "GeneralConnectionStrategy";

    public Gen1GimbalStrategy(ExecutorService executor) {
        super(executor);
    }

    @Override
    protected void onConnectionSuccess(AbstractBluetoothDevice device) {
        super.onConnectionSuccess(device);
        //startHeartbeat(device);
    }


    @Override
    protected byte[] initHeartbeatData() {
        byte[] data = new byte[20];
        data[0] = 0x55;
        data[1] = 0x07;
        data[2] = 0x60;
        data[3] = 0x14;
        data[4] = (byte) 0xc2;
        data[5] = (byte) 0xf7;
        data[6] = (byte) 0x83;
        return data;
    }
}

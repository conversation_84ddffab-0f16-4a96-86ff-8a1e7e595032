package com.blink.blinkfocos.util

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import com.blink.blinkfocos.sensor.SensorFusion

class SensorUtil(context: Context, var callback: Callback?) : SensorEventListener {
    private var sensorManager: SensorManager? = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val rotationMatrix = FloatArray(9)
    private val orientations = FloatArray(3)
    private var rotationVectorSensor: Sensor? = sensorManager?.getDefaultSensor(Sensor.TYPE_ROTATION_VECTOR)
    private val sensorFusion = SensorFusion()
    private var isInitialized = false
    private var isYawInitialized = false
    private var initialAzimuth = 0.0
    private var initialPitch = 0.0

    fun registerListener() {
        sensorManager?.run {
            registerListener(
                this@SensorUtil,
                getDefaultSensor(Sensor.TYPE_ACCELEROMETER),
                SensorManager.SENSOR_DELAY_GAME
            )
            registerListener(
                this@SensorUtil,
                getDefaultSensor(Sensor.TYPE_GYROSCOPE),
                SensorManager.SENSOR_DELAY_GAME
            )
            registerListener(
                this@SensorUtil,
                getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD),
                SensorManager.SENSOR_DELAY_GAME
            )
        }
//        sensorManager?.registerListener(this, rotationVectorSensor, SensorManager.SENSOR_DELAY_GAME)
    }

    fun unregisterListener() {
        sensorManager?.unregisterListener(this)
        callback = null
        sensorManager = null
//        sensorFusion.stopTimer()
    }

    fun setIsInitialAzimuthSet(azimuth :Boolean) {
        isInitialized = azimuth
    }

    fun setIsYawInitial(isYawInitialized: Boolean) {
        this.isYawInitialized = isYawInitialized
    }

    override fun onSensorChanged(event: SensorEvent?) {
        event?.run {
            when (sensor.type) {
                Sensor.TYPE_ACCELEROMETER -> {
                    sensorFusion.setAccel(values)
                    sensorFusion.calculateAccMagOrientation()
                }
                Sensor.TYPE_GYROSCOPE -> {
                    sensorFusion.gyroFunction(this)
                }
                Sensor.TYPE_MAGNETIC_FIELD -> {
                    sensorFusion.setMagnet(values)
                }
            }
            updateOrientationDisplay()
        }
    }

    private fun updateOrientationDisplay() {
        callback?.isFusionReady(sensorFusion.isFusionReady)
        if (!sensorFusion.isFusionReady) {
            return
        }
        val azimuth = sensorFusion.azimuth
        val pitch = sensorFusion.roll

        if (!isInitialized) {
            initialAzimuth = azimuth
            initialPitch = pitch
            isInitialized = true
        }
        if (!isYawInitialized) {
            initialAzimuth = azimuth
            isYawInitialized = true
        }

        var azimuthChange = azimuth - initialAzimuth
        if (azimuthChange < -180) azimuthChange += 360
        else if (azimuthChange > 180) azimuthChange -= 360

        var pitchChange = pitch - initialPitch
        if (pitchChange < -180) pitchChange += 360
        else if (pitchChange > 180) pitchChange -= 360

        callback?.azimuthChange(
            azimuth.toInt(),
            azimuthChange.toInt(),
            pitch.toInt(),
            pitchChange.toInt(),
            initialAzimuth.toInt(),
            initialPitch.toInt()
        )
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {}

    interface Callback {
        fun azimuthChange(test: Int, yawChange: Int, testPitch: Int, pitchChange: Int, initialAzimuth: Int, initialPitch: Int)
        fun isFusionReady(isFusionReady: Boolean)
    }
}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:keepScreenOn="true"
    android:orientation="vertical"
    tools:context=".module.my.AccountSettingActivity">

    <include layout="@layout/fast_layout_title_bar_land"
        android:layout_height="40dp"
        android:layout_width="match_parent"/>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_account_change_password"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:foreground="?android:attr/selectableItemBackground"
        android:gravity="center_vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="55dp"
            android:text="@string/change_password"
            android:textColor="@color/tv_black"
            android:textSize="13dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_weight="1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="15dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="66dp"
            android:src="@drawable/ic_gray_right"
            android:tint="@color/black" />
    </androidx.appcompat.widget.LinearLayoutCompat>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="55dp"
        android:layout_marginEnd="55dp"
        android:background="@color/view_line_gray" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_account_delete"
        android:layout_width="match_parent"
        android:layout_height="47dp"
        android:foreground="?android:attr/selectableItemBackground"
        android:gravity="center_vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="55dp"
            android:text="@string/delete_account"
            android:textColor="@color/tv_black"
            android:textSize="13dp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1px"
            android:layout_weight="1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="15dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="66dp"
            android:src="@drawable/ic_gray_right"
            android:tint="@color/black" />
    </androidx.appcompat.widget.LinearLayoutCompat>


    <View
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_marginLeft="55dp"
        android:background="@color/view_thick_line_gray" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/authorized"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="55dp"
        android:layout_marginTop="16dp"
        android:text="@string/authorized"
        android:textColor="@color/tv_black"
        android:textSize="13dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="55dp"
        android:layout_marginRight="55dp"
        android:background="#F7F7F7"
        android:orientation="vertical">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/ll_facebook"
            android:layout_width="match_parent"
            android:layout_height="47dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="29dp"
                android:layout_height="29dp"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="29dp"
                android:src="@drawable/ic_facebook" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="7dp"
                android:text="@string/facebook"
                android:textColor="@color/tv_black"
                android:textSize="13dp" />

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <com.blink.blinkfocos.widget.SwitchButtonView
                android:id="@+id/switch_facebook"
                android:layout_width="44dp"
                android:layout_height="25dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="29dp"
                app:status="false"
                app:switch_off_color="@color/switch_off"
                app:switch_on_color="@color/style_red" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </LinearLayout>
</LinearLayout>
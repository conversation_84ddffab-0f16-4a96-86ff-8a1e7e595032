<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#fff"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_sava_dialog_title"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:text="@string/reminder"
            android:textColor="@color/tv_black"
            android:textSize="17sp" />

        <ScrollView
            android:layout_width="wrap_content"
            android:layout_height="220dp">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:id="@+id/ll_read_priavate"
                    android:paddingLeft="@dimen/dp_20"
                    android:paddingRight="@dimen/dp_20">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_sava_dialog_message"
                        android:layout_width="wrap_content"
                        android:textColorLink="@color/orange_setting_select"
                        android:layout_height="wrap_content"
                        android:text="@string/splash_notice"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="18dp"
                        android:textSize="@dimen/sp_15" />
                    <TextView

                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/priavate6"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="18dp"
                        android:textSize="@dimen/sp_15" />
                    <TextView
                        android:id="@+id/tv_save_dialog_message2"
                        android:layout_width="wrap_content"
                        android:textColorLink="@color/orange_setting_select"
                        android:layout_height="wrap_content"
                        android:text="@string/splash_notice2"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="18dp"
                        android:textSize="@dimen/sp_15"
                        />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/priavate1"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="11dp"
                        android:textSize="@dimen/sp_11" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/priavate2"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="11dp"
                        android:textSize="@dimen/sp_11" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/priavate3"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="11dp"
                        android:textSize="@dimen/sp_11" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/priavate4"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="11dp"
                        android:textSize="@dimen/sp_11" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/priavate5"
                        android:textColor="@color/tv_black"
                        android:layout_marginBottom="11dp"
                        android:textSize="@dimen/sp_11" />

                </LinearLayout>
            </RelativeLayout>
        </ScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal"
            >

            <TextView
                android:id="@+id/tv_sava_dialog_cancel"
                android:layout_width="0dp"
                android:layout_height="match_parent"

                android:layout_weight="1"
                android:background="#a1a1a1"
                android:clickable="true"
                android:foreground="?android:selectableItemBackground"
                android:gravity="center"
                android:text="@string/quit_app"
                android:textColor="#867575"
                android:textSize="15sp" />


            <TextView
                android:id="@+id/tv_sava_dialog_confirm"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="1dp"

                android:layout_weight="1"
                android:background="@color/orange_setting_select"
                android:clickable="true"
                android:foreground="?android:selectableItemBackground"
                android:gravity="center"
                android:text="@string/confirm"
                android:textColor="#efebeb"
                android:textSize="15sp" />

        </LinearLayout>
    </LinearLayout>

</LinearLayout>

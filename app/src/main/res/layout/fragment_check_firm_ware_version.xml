<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".module.my.CheckFirmWareVersionFragment">

    <TextView
        android:id="@+id/tv_sn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:textColor="@color/tv_black_999999"
        android:textSize="12dp"
        android:lineSpacingExtra="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar_headFastLib" />

    <com.aries.ui.view.title.TitleBarView
        android:id="@+id/titleBar_headFastLib"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_title_height"
        android:background="@drawable/bg_titlebar"
        app:layout_constraintTop_toTopOf="parent"
        app:title_actionTextColor="@color/white"
        app:title_dividerBackground="@color/colorTitleDivider"
        app:title_dividerHeight="@dimen/dp_line_size"
        app:title_dividerVisible="false"
        app:title_leftTextColor="@color/white"
        app:title_rightTextColor="@color/white"
        app:title_titleMainText="@string/firmware_update"
        app:title_titleMainTextColor="@color/white"
        app:title_titleMainTextFakeBold="true"
        app:title_titleMainTextSize="16dp"
        app:title_titleSubTextColor="@color/white" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:text="@string/checking"
        android:textColor="@color/black"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/titleBar_headFastLib" />


    <ImageView
        android:id="@+id/iv_firmware"
        android:layout_width="88dp"
        android:layout_height="88dp"
        android:layout_marginTop="74dp"
        android:background="@drawable/shape_circle_gray"
        android:paddingHorizontal="22dp"
        android:paddingVertical="19dp"
        android:src="@drawable/icon_firm_ware"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_tip"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="17dp"
        android:layout_marginTop="18dp"
        android:gravity="center"
        android:lineSpacingExtra="3dp"
        android:textColor="@color/black"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_firmware" />

    <ProgressBar
        android:id="@+id/progressbar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:indeterminateDrawable="@drawable/drawable_round_progress"
        app:layout_constraintBottom_toBottomOf="@+id/iv_firmware"
        app:layout_constraintEnd_toEndOf="@+id/iv_firmware"
        app:layout_constraintStart_toStartOf="@+id/iv_firmware"
        app:layout_constraintTop_toTopOf="@+id/iv_firmware" />


    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="10dp"
        android:layout_marginTop="60dp"
        android:background="#f7f7f7"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_tip" />

    <TextView
        android:id="@+id/tv_update_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="29dp"
        android:layout_marginTop="25dp"
        android:layout_marginBottom="10dp"
        android:lineSpacingMultiplier="1.2"
        android:textColor="#4d4d4d"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line"
        app:layout_constraintBottom_toTopOf="@+id/bt_next"/>

    <com.blink.blinkfocos.widget.MyButton
        android:id="@+id/bt_next"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="29dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/bg_button_comfirm"
        android:text="@string/update_right_now"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="15dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>
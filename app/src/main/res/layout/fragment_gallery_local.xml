<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.blink.blinkfocos.widget.MyViewPager
        android:id="@+id/viewpager_local"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        app:canScroll="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_local" />

    <com.aries.ui.view.tab.CommonTabLayout
        android:id="@+id/tab_local"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_gravity="center_vertical"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tl_indicator_color="@color/orange_setting_select"
        app:tl_indicator_height="2dp"
        app:tl_tab_padding="6dp"
        app:tl_textBold="SELECT"
        app:tl_textSelectColor="@color/orange_setting_select"
        app:tl_textSelectSize="16dp"
        app:tl_textSize="16dp"
        app:tl_textUnSelectColor="@color/et_hine_color" />

</androidx.constraintlayout.widget.ConstraintLayout>
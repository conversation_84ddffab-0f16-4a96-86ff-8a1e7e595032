<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="#50000000"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="306dp"
        android:layout_height="306dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="103dp"
            android:background="@drawable/shape_conner_dc4707_13dp_top" />

        <View
            android:layout_width="match_parent"
            android:layout_height="253dp"
            android:layout_gravity="bottom"
            android:background="@drawable/shape_conner_white_start_live_bottom" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="57dp">


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/scoreboard"
                android:textColor="@color/white"
                android:textSize="16dp" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_close_pin_show"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="11dp"
                android:paddingStart="5dp"
                android:paddingEnd="10dp"
                android:src="@drawable/ic_x"
                android:tint="@color/white" />
        </RelativeLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="253dp"
            android:layout_gravity="bottom"
            android:orientation="vertical"
            android:visibility="visible"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="25dp"
                android:layout_marginRight="10dp"
                android:gravity="center"
                android:text="@string/please_open_the_following_link"
                android:textColor="@color/tv_black_232323"
                android:textSize="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_url"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/scoreboard_link"
                android:textColor="@color/tv_black_232323"
                android:textSize="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_pin_show_title" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_desc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:gravity="center"
                android:text="@string/enter_the_invitation_code"
                android:textColor="@color/tv_black_232323"
                android:textSize="12dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_pin_show_url" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_first"
                android:layout_width="40dp"
                android:layout_height="50dp"
                android:layout_marginBottom="60dp"
                android:background="@drawable/bg_pin_show_show"
                android:gravity="center"
                android:text="A"
                android:textColor="@color/tv_black_232323"
                android:textSize="27dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/tv_pin_show_second" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_second"
                android:layout_width="40dp"
                android:layout_height="50dp"
                android:layout_marginLeft="4dp"
                android:background="@drawable/bg_pin_show_show"
                android:gravity="center"
                android:text="7"
                android:textColor="@color/tv_black_232323"
                android:textSize="27dp"
                app:layout_constraintLeft_toRightOf="@id/tv_pin_show_first"
                app:layout_constraintRight_toLeftOf="@id/tv_pin_show_third"
                app:layout_constraintTop_toTopOf="@id/tv_pin_show_first" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_third"
                android:layout_width="40dp"
                android:layout_height="50dp"
                android:layout_marginLeft="4dp"
                android:background="@drawable/bg_pin_show_show"
                android:gravity="center"
                android:text="2"
                android:textColor="@color/tv_black_232323"
                android:textSize="27dp"
                app:layout_constraintLeft_toRightOf="@id/tv_pin_show_second"
                app:layout_constraintRight_toLeftOf="@id/tv_pin_show_forth"
                app:layout_constraintTop_toTopOf="@id/tv_pin_show_second" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_show_forth"
                android:layout_width="40dp"
                android:layout_height="50dp"
                android:layout_marginLeft="4dp"
                android:background="@drawable/bg_pin_show_show"
                android:gravity="center"
                android:text="B"
                android:textColor="@color/tv_black_232323"
                android:textSize="27dp"
                app:layout_constraintLeft_toRightOf="@id/tv_pin_show_third"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_pin_show_third" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_pin_time_out"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="14dp"
                android:gravity="center"
                android:text="@string/the_invitation_code_is_invalid"
                android:textColor="@color/tv_black_232323"
                android:textSize="12dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
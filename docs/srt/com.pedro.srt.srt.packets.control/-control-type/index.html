<!doctype html>
<html class="no-js">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" charset="UTF-8">
    <title>ControlType</title>
<link href="../../../images/logo-icon.svg" rel="icon" type="image/svg">    <script>var pathToRoot = "../../../";</script>
    <script>document.documentElement.classList.replace("no-js","js");</script>
    <script>const storage = localStorage.getItem("dokka-dark-mode")
    if (storage == null) {
        const osDarkSchemePreferred = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        if (osDarkSchemePreferred === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    } else {
        const savedDarkMode = JSON.parse(storage)
        if(savedDarkMode === true) {
            document.getElementsByTagName("html")[0].classList.add("theme-dark")
        }
    }
    </script>
<script type="text/javascript" src="https://unpkg.com/kotlin-playground@1/dist/playground.min.js" async></script>
<script type="text/javascript" src="../../../scripts/sourceset_dependencies.js" async></script>
<link href="../../../styles/style.css" rel="Stylesheet">
<link href="../../../styles/main.css" rel="Stylesheet">
<link href="../../../styles/prism.css" rel="Stylesheet">
<link href="../../../styles/logo-styles.css" rel="Stylesheet">
<link href="../../../styles/font-jb-sans-auto.css" rel="Stylesheet">
<link href="../../../ui-kit/ui-kit.min.css" rel="Stylesheet">
<script type="text/javascript" src="../../../scripts/clipboard.js" async></script>
<script type="text/javascript" src="../../../scripts/navigation-loader.js" async></script>
<script type="text/javascript" src="../../../scripts/platform-content-handler.js" async></script>
<script type="text/javascript" src="../../../scripts/main.js" defer></script>
<script type="text/javascript" src="../../../scripts/prism.js" async></script>
<script type="text/javascript" src="../../../ui-kit/ui-kit.min.js" defer></script>
<script type="text/javascript" src="../../../scripts/symbol-parameters-wrapper_deferred.js" defer></script></head>
<body>
    <div class="root">
    <nav class="navigation theme-dark" id="navigation-wrapper">
<a class="library-name--link" href="../../../index.html">
                    RootEncoder
            </a>        <button class="navigation-controls--btn navigation-controls--btn_toc ui-kit_mobile-only" id="toc-toggle" type="button">Toggle table of contents
        </button>
        <div class="navigation-controls--break ui-kit_mobile-only"></div>
        <div class="library-version" id="library-version">
        </div>
        <div class="navigation-controls">
        <div class="filter-section filter-section_loading" id="filter-section">
                <button class="platform-tag platform-selector jvm-like" data-active="" data-filter=":srt:dokkaHtmlPartial/release">androidJvm</button>
            <div class="dropdown filter-section--dropdown" data-role="dropdown" id="filter-section-dropdown">
                <button class="button button_dropdown filter-section--dropdown-toggle" role="combobox" data-role="dropdown-toggle" aria-controls="platform-tags-listbox" aria-haspopup="listbox" aria-expanded="false" aria-label="Toggle source sets"></button>
                <ul role="listbox" id="platform-tags-listbox" class="dropdown--list" data-role="dropdown-listbox">
                    <div class="dropdown--header"><span>Platform filter</span>
                        <button class="button" data-role="dropdown-toggle" aria-label="Close platform filter">
                            <i class="ui-kit-icon ui-kit-icon_cross"></i>
                        </button>
                    </div>
                        <li role="option" class="dropdown--option platform-selector-option jvm-like" tabindex="0">
                            <label class="checkbox">
                                <input type="checkbox" class="checkbox--input" id=":srt:dokkaHtmlPartial/release" data-filter=":srt:dokkaHtmlPartial/release">
                                <span class="checkbox--icon"></span>
                                androidJvm
                            </label>
                        </li>
                </ul>
                <div class="dropdown--overlay"></div>
            </div>
        </div>
            <button class="navigation-controls--btn navigation-controls--btn_theme" id="theme-toggle-button" type="button">Switch theme
            </button>
            <div class="navigation-controls--btn navigation-controls--btn_search" id="searchBar" role="button">Search in
                API
            </div>
        </div>
    </nav>
        <div id="container">
            <div class="sidebar" id="leftColumn">
                <div class="dropdown theme-dark_mobile" data-role="dropdown" id="toc-dropdown">
                    <ul role="listbox" id="toc-listbox" class="dropdown--list dropdown--list_toc-list" data-role="dropdown-listbox">
                        <div class="dropdown--header">
                            <span>
                                    RootEncoder
                            </span>
                            <button class="button" data-role="dropdown-toggle" aria-label="Close table of contents">
                                <i class="ui-kit-icon ui-kit-icon_cross"></i>
                            </button>
                        </div>
                        <div class="sidebar--inner" id="sideMenu"></div>
                    </ul>
                    <div class="dropdown--overlay"></div>
                </div>
            </div>
            <div id="main">
<div class="main-content" data-page-type="classlike" id="content" pageids="srt::com.pedro.srt.srt.packets.control/ControlType///PointingToDeclaration//1600112080">
  <div class="breadcrumbs"><a href="../../index.html">srt</a><span class="delimiter">/</span><a href="../index.html">com.pedro.srt.srt.packets.control</a><span class="delimiter">/</span><span class="current">ControlType</span></div>
  <div class="cover ">
    <h1 class="cover"><span>Control</span><wbr><span><span>Type</span></span></h1>
    <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">enum </span><a href="index.html">ControlType</a> : <a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-enum/index.html">Enum</a><span class="token operator">&lt;</span><a href="index.html">ControlType</a><span class="token operator">&gt; </span></div><p class="paragraph">Created by pedro on 21/8/23.</p></div></div>
  </div>
  <div class="tabbedcontent">
    <div class="tabs-section" tabs-section="tabs-section"><button class="section-tab" data-active="" data-togglable="CONSTRUCTOR,TYPE,PROPERTY,FUNCTION">Members</button><button class="section-tab" data-togglable="ENTRY">Entries</button></div>
    <div class="tabs-section-body">
      <div data-togglable="ENTRY">
        <h2 class="">Entries</h2>
        <div class="table"><a data-name="1205318942%2FClasslikes%2F1600112080" anchor-label="HANDSHAKE" id="1205318942%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-h-a-n-d-s-h-a-k-e/index.html">HANDSHAKE</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1205318942%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-h-a-n-d-s-h-a-k-e/index.html">HANDSHAKE</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-701845964%2FClasslikes%2F1600112080" anchor-label="KEEP_ALIVE" id="-701845964%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-k-e-e-p_-a-l-i-v-e/index.html">KEEP_ALIVE</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-701845964%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-k-e-e-p_-a-l-i-v-e/index.html">KEEP_ALIVE</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="1190769776%2FClasslikes%2F1600112080" anchor-label="ACK" id="1190769776%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-a-c-k/index.html">ACK</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="1190769776%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-a-c-k/index.html">ACK</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-91829057%2FClasslikes%2F1600112080" anchor-label="NAK" id="-91829057%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-n-a-k/index.html">NAK</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-91829057%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-n-a-k/index.html">NAK</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="179669349%2FClasslikes%2F1600112080" anchor-label="CONGESTION_WARNING" id="179669349%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-c-o-n-g-e-s-t-i-o-n_-w-a-r-n-i-n-g/index.html">CONGESTION_WARNING</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="179669349%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-c-o-n-g-e-s-t-i-o-n_-w-a-r-n-i-n-g/index.html">CONGESTION_WARNING</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="13293847%2FClasslikes%2F1600112080" anchor-label="SHUTDOWN" id="13293847%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-s-h-u-t-d-o-w-n/index.html">SHUTDOWN</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="13293847%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-s-h-u-t-d-o-w-n/index.html">SHUTDOWN</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-355030294%2FClasslikes%2F1600112080" anchor-label="ACK2" id="-355030294%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-a-c-k2/index.html">ACK2</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-355030294%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-a-c-k2/index.html">ACK2</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1428800529%2FClasslikes%2F1600112080" anchor-label="DROP_REQ" id="-1428800529%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-d-r-o-p_-r-e-q/index.html">DROP_REQ</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1428800529%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-d-r-o-p_-r-e-q/index.html">DROP_REQ</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="893225356%2FClasslikes%2F1600112080" anchor-label="PEER_ERROR" id="893225356%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-p-e-e-r_-e-r-r-o-r/index.html">PEER_ERROR</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="893225356%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-p-e-e-r_-e-r-r-o-r/index.html">PEER_ERROR</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1707406986%2FClasslikes%2F1600112080" anchor-label="USER_DEFINED" id="-1707406986%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-u-s-e-r_-d-e-f-i-n-e-d/index.html">USER_DEFINED</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1707406986%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-u-s-e-r_-d-e-f-i-n-e-d/index.html">USER_DEFINED</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-849243782%2FClasslikes%2F1600112080" anchor-label="SUB_TYPE" id="-849243782%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-togglable="ENTRY" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-s-u-b_-t-y-p-e/index.html">SUB_TYPE</a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-849243782%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><div class="block"><a href="-s-u-b_-t-y-p-e/index.html">SUB_TYPE</a></div></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="TYPE">
        <h2 class="">Types</h2>
        <div class="table"><a data-name="-332442382%2FClasslikes%2F1600112080" anchor-label="Companion" id="-332442382%2FClasslikes%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="-companion/index.html"><span><span>Companion</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-332442382%2FClasslikes%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">object </span><a href="-companion/index.html">Companion</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="PROPERTY">
        <h2 class="">Properties</h2>
        <div class="table"><a data-name="559834267%2FProperties%2F1600112080" anchor-label="entries" id="559834267%2FProperties%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="entries.html"><span><span>entries</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="559834267%2FProperties%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">val </span><a href="entries.html">entries</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin.enums/-enum-entries/index.html">EnumEntries</a><span class="token operator">&lt;</span><a href="index.html">ControlType</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Returns a representation of an immutable list of all enum entries, in the order they're declared.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-372974862%2FProperties%2F1600112080" anchor-label="name" id="-372974862%2FProperties%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../com.pedro.srt.srt.packets.data/-packet-position/-s-i-n-g-l-e/index.html#-372974862%2FProperties%2F1600112080"><span><span>name</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-372974862%2FProperties%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">val </span><a href="../../com.pedro.srt.srt.packets.data/-packet-position/-s-i-n-g-l-e/index.html#-372974862%2FProperties%2F1600112080">name</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-739389684%2FProperties%2F1600112080" anchor-label="ordinal" id="-739389684%2FProperties%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="../../com.pedro.srt.srt.packets.data/-packet-position/-s-i-n-g-l-e/index.html#-739389684%2FProperties%2F1600112080"><span><span>ordinal</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-739389684%2FProperties%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">val </span><a href="../../com.pedro.srt.srt.packets.data/-packet-position/-s-i-n-g-l-e/index.html#-739389684%2FProperties%2F1600112080">ordinal</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1209931174%2FProperties%2F1600112080" anchor-label="value" id="-1209931174%2FProperties%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="value.html"><span><span>value</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1209931174%2FProperties%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">val </span><a href="value.html">value</a><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-int/index.html">Int</a></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div data-togglable="FUNCTION">
        <h2 class="">Functions</h2>
        <div class="table"><a data-name="-382137119%2FFunctions%2F1600112080" anchor-label="valueOf" id="-382137119%2FFunctions%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="value-of.html"><span>value</span><wbr><span><span>Of</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-382137119%2FFunctions%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="value-of.html"><span class="token function">valueOf</span></a><span class="token punctuation">(</span><span class="parameters "><span class="parameter ">value<span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-string/index.html">String</a></span></span><span class="token punctuation">)</span><span class="token operator">: </span><a href="index.html">ControlType</a></div><div class="brief "><p class="paragraph">Returns the enum constant of this type with the specified name. The string must match exactly an identifier used to declare an enum constant in this type. (Extraneous whitespace characters are not permitted.)</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
<a data-name="-1581873235%2FFunctions%2F1600112080" anchor-label="values" id="-1581873235%2FFunctions%2F1600112080" data-filterable-set=":srt:dokkaHtmlPartial/release"></a>
          <div class="table-row" data-filterable-current=":srt:dokkaHtmlPartial/release" data-filterable-set=":srt:dokkaHtmlPartial/release">
            <div class="main-subrow keyValue ">
              <div class=""><span class="inline-flex">
                  <div><a href="values.html"><span><span>values</span></span></a></div>
<span class="anchor-wrapper"><span class="anchor-icon" pointing-to="-1581873235%2FFunctions%2F1600112080"></span>
                    <div class="copy-popup-wrapper "><span class="copy-popup-icon"></span><span>Link copied to clipboard</span></div>
                  </span></span></div>
              <div>
                <div class="title">
                  <div class="platform-hinted " data-platform-hinted="data-platform-hinted"><div class="content sourceset-dependent-content" data-active="" data-togglable=":srt:dokkaHtmlPartial/release"><div class="symbol monospace"><span class="token keyword">fun </span><a href="values.html"><span class="token function">values</span></a><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">: </span><a href="https://kotlinlang.org/api/core/kotlin-stdlib/kotlin/-array/index.html">Array</a><span class="token operator">&lt;</span><a href="index.html">ControlType</a><span class="token operator">&gt;</span></div><div class="brief "><p class="paragraph">Returns an array containing the constants of this enum type, in the order they're declared.</p></div></div></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
    <div class="footer">
        <a href="#content" id="go-to-top-link" class="footer--button footer--button_go-to-top"></a>
        <span>© 2025 Copyright</span>
        <span class="pull-right">
            <span>Generated by </span>
            <a class="footer--link footer--link_external" href="https://github.com/Kotlin/dokka">
                <span>dokka</span>
            </a>
        </span>
    </div>
            </div>
        </div>
    </div>
</body>
</html>

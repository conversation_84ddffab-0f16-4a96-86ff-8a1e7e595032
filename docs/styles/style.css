/*
 * Copyright 2014-2024 JetBrains s.r.o. Use of this source code is governed by the Apache 2.0 license.
 */

@import url('./font-jb-sans-auto.css');
@import url('https://fonts.googleapis.com/css?family=JetBrains+Mono');

/* --- root styles --- */
:root {
    --default-gray: #f4f4f4;
    --default-font-color: rgb(0, 0, 0);
    --header-font-color: var(--default-font-color);

    --breadcrumb-font-color: #637282;
    --breadcrumb-margin: 24px;
    --hover-link-color: #5B5DEF;

    --footer-height: 64px;
    --footer-padding-top: 48px;
    --footer-background: var(--default-gray);
    --footer-font-color: var(--average-color);
    --footer-go-to-top-color: white;

    --horizontal-spacing-for-content: 16px;
    --bottom-spacing: 16px;
    --color-scrollbar: rgba(39, 40, 44, 0.40);
    --color-scrollbar-track: var(--default-gray);
    --default-white: #fff;
    --background-color: var(--default-white);
    --dark-mode-and-search-icon-color: var(--default-white);
    --color-dark: #27282c;
    --default-font-family: JetBrains Sans, Inter, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Droid Sans, Helvetica Neue, Arial, sans-serif;
    --default-monospace-font-family: JetBrains Mono, SFMono-Regular, Consolas, Liberation Mono, Menlo, Courier, monospace;
    --default-font-size: 15px;
    --average-color: var(--color-dark);
    --brief-color: var(--average-color);
    --copy-icon-color: rgba(39, 40, 44, .7);
    --copy-icon-hover-color: var(--color-dark);
    --code-background: rgba(39, 40, 44, .05);
    --border-color: rgba(39, 40, 44, .2);
    --navigation-highlight-color: rgba(39, 40, 44, 0.05);
    --top-navigation-height: 73px;
    --max-width: 1160px;
    --white-10: hsla(0, 0%, 100%, .1);

    --active-tab-border-color: #7F52FF;
    --inactive-tab-border-color: rgba(164, 164, 170, 0.7);

    --active-section-color: #7F52FF;
    --inactive-section-color: rgba(25, 25, 28, .7);

    --sidebar-width: 280px;
    --sidemenu-section-active-color: #7F52FF;
}

html {
    height: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    scrollbar-color: rgba(39, 40, 44, 0.40) #F4F4F4;
    scrollbar-color: var(--color-scrollbar) var(--color-scrollbar-track);
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    color: var(--default-font-color);
}

html ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

html ::-webkit-scrollbar-track {
    background-color: var(--color-scrollbar-track);
}

html ::-webkit-scrollbar-thumb {
    width: 8px;
    border-radius: 6px;
    background: rgba(39, 40, 44, 0.40);
    background: var(--color-scrollbar);
}

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
}

/* /--- root styles --- */

/* --- global tags styles --- */
body, table {
    background: var(--background-color);
    font-family: var(--default-font-family);
    font-style: normal;
    font-weight: normal;
    font-size: var(--default-font-size);
    line-height: 1.6;
    margin: 0;
}

h1 {
    font-size: 40px;
    line-height: 48px;
    letter-spacing: -1px;
}

h2 {
    font-size: 31px;
    line-height: 40px;
    letter-spacing: -0.5px;
}

h3 {
    font-size: 20px;
    line-height: 28px;
    letter-spacing: -0.2px;
}

p, ul, ol, table, pre, dl {
    margin: 0;
}

a {
    text-decoration: none;
}

u {
    text-decoration: none;
    padding-bottom: 2px;
    border-bottom: 1px solid var(--border-color);
}

blockquote {
    border-left: 1ch solid var(--default-gray);
    margin: 0;
    padding-left: 1ch;
    font-style: italic;
    color: var(--average-color);
}

.theme-dark blockquote {
    color: var(--default-font-color);
    border-left-color: var(--code-background);
}

pre {
    display: block;
}

dt {
    color: #444;
    font-weight: 530;
}

img {
    max-width: 100%;
}

small {
    font-size: 11px;
}

table {
    width: 100%;
    border-collapse: collapse;
    padding: 5px;
}

th, td {
    padding: 12px 10px 11px;
    text-align: left;
    vertical-align: top;
}

tbody > tr {
    min-height: 56px;
}

td:first-child {
    width: 20vw;
}

/* /---  global tags styles --- */

/* ---  utils classes --- */
.w-100 {
    width: 100%;
}

.no-gutters {
    margin: 0;
    padding: 0;
}

.d-flex {
    display: flex;
}

.floating-right {
    float: right;
}

.pull-right {
    float: right;
    margin-left: auto
}

.clearfix::after {
    display: block;
    content: '';
    clear: both;
    height: 0;
}

/* /---  utils classes --- */

/* ---dark theme --- */
.theme-dark {
    --background-color: #262628;
    --color-dark: #3d3d41;
    --default-font-color: rgba(255, 255, 255, 0.96);
    --border-color: hsla(0, 0%, 100%, 0.2);
    --code-background: hsla(0, 0%, 100%, 0.05);
    --breadcrumb-font-color: #8c8c8e;
    --brief-color: hsla(0, 0%, 100%, 0.4);
    --copy-icon-color: hsla(0, 0%, 100%, 0.6);
    --copy-icon-hover-color: #fff;

    --active-tab-border-color: var(--default-font-color);
    --inactive-tab-border-color: hsla(0, 0%, 100%, 0.4);

    --active-section-color: var(--default-font-color);
    --inactive-section-color: hsla(0, 0%, 100%, 0.4);

    --navigation-highlight-color: rgba(255, 255, 255, 0.05);
    --footer-background: hsla(0, 0%, 100%, 0.05);
    --footer-font-color: hsla(0, 0%, 100%, 0.6);
    --footer-go-to-top-color: var(--footer-font-color);

    --sidemenu-section-active-color: var(--color-dark);
}

/* /---dark theme --- */

.root {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* --- Layout styles --- */

#container {
    display: flex;
    flex: 1 1 auto;
    min-height: 0; /* full height exclude header */
}

#container > #main {
    overflow: auto;
}

#main {
    display: flex;
    flex-direction: column;
    flex: 1 1 0; /* full width, but no affects for sidebar */
}

/* /--- Layout styles --- */

/* --- Main Content styles --- */
.main-content {
    padding-bottom: var(--bottom-spacing);
    margin-left: auto;
    margin-right: auto;
    max-width: var(--max-width);
    width: 100%;
    z-index: 0;
}

.main-content > * {
    margin-left: var(--horizontal-spacing-for-content);
    margin-right: var(--horizontal-spacing-for-content);
}

.main-content .content > hr {
    margin: 30px 0;
    border-top: 3px double #8c8b8b;
}

.main-content :is(h1, h2) {
    font-weight: 530;
}

/* /--- Main Content styles --- */

/* /--- Breadcrumbs styles --- */
.breadcrumbs, .breadcrumbs a, .breadcrumbs a:hover {
    margin-top: var(--breadcrumb-margin);
    color: var(--breadcrumb-font-color);
    overflow-wrap: break-word;
}

.breadcrumbs .delimiter {
    margin: auto 2px;
}

.breadcrumbs .current {
    color: var(--default-font-color);
}

/* /--- Breadcrumbs styles --- */

.cover > .platform-hinted {
    padding-bottom: 12px;
}

.cover {
    display: flex;
    flex-direction: column;
}

.cover .platform-hinted.with-platform-tabs .sourceset-dependent-content > .block ~ .symbol {
    padding-top: 16px;
    padding-left: 0;
}

.cover .sourceset-dependent-content > .block {
    padding: 16px 0;
    font-size: 18px;
    line-height: 28px;
}

.cover .platform-hinted.with-platform-tabs .sourceset-dependent-content > .block {
    padding: 0;
    font-size: var(--default-font-size);
}

.cover ~ .divergent-group {
    margin-top: 24px;
    padding: 24px 8px 8px 8px;
}

.cover ~ .divergent-group .main-subrow .symbol {
    width: 100%;
}

.main-content p.paragraph,
.sample-container, blockquote,
.content > .symbol {
    margin-top: 8px;
}

blockquote,
.content > .symbol:first-of-type,
p.paragraph:first-child,
.brief p.paragraph {
    margin-top: 0;
}

.content .kdoc-tag > p.paragraph {
    margin-top: 0;
}

.content h4 {
    margin-bottom: 0;
}

.divergent-group {
    background-color: var(--background-color);
    padding: 16px 0 8px 0;
    margin-bottom: 2px;
}

.divergent-group .table-row, tbody > tr {
    border-bottom: 1px solid var(--border-color);
}

.divergent-group .table-row:last-of-type, tbody > tr:last-of-type {
    border-bottom: none;
}

.title > .divergent-group:first-of-type {
    padding-top: 0;
}

.sample-container, div.CodeMirror {
    position: relative;
    display: flex;
    flex-direction: column;
}

code.paragraph {
    display: block;
}

.strikethrough {
    text-decoration: line-through;
}

/* Workaround for Firefox  https://github.com/Kotlin/dokka/issues/3156 */
@-moz-document url-prefix() {
    .strikethrough {
        position: relative;
        text-decoration: none;
    }

    /* complex selectors here are required to handle multiline cases */
    .strikethrough::after, .strikethrough span:after  {
        content: '';
        position: absolute;
        top: 7px;
        left: 0;
        right: 0;
        height: 1px;
        background-color: currentColor;
        z-index: 1;
    }
}

.symbol:empty {
    padding: 0;
}

.symbol:not(.token), code {
    background-color: var(--code-background);
    align-items: center;
    box-sizing: border-box;
    white-space: pre-wrap;
    font-family: var(--default-monospace-font-family);
    font-size: var(--default-font-size);
}

.symbol:not(.token), code.block {
    display: block;
    padding: 12px 32px 12px 12px;
    border-radius: 8px;
    line-height: 24px;
    position: relative;
}

code {
    overflow-x: auto;
    max-width: 100%;
}

code:not(.block) {
    display: inline-block;
    vertical-align: bottom;
}

.symbol > a {
    color: var(--hover-link-color);
}

.copy-icon {
    cursor: pointer;
}

.sample-container span.copy-icon {
    display: none;
}

.js .sample-container:hover span.copy-icon {
    display: inline-block;
}

.sample-container span.copy-icon::before {
    width: 24px;
    height: 24px;
    display: inline-block;
    content: '';
    /* masks are required if you want to change color of the icon dynamically instead of using those provided with the SVG */
    -webkit-mask: url("../images/copy-icon.svg") no-repeat 50% 50%;
    mask: url("../images/copy-icon.svg") no-repeat 50% 50%;
    -webkit-mask-size: cover;
    mask-size: cover;
    background-color: var(--copy-icon-color);
}

.sample-container span.copy-icon:hover::before {
    background-color: var(--copy-icon-hover-color);
}

.copy-popup-wrapper {
    display: none;
    align-items: center;
    position: absolute;
    z-index: 1000;
    background: var(--background-color);
    font-weight: normal;
    font-family: var(--default-font-family);
    width: max-content;
    font-size: var(--default-font-size);
    cursor: default;
    border: 1px solid #D8DCE1;
    box-sizing: border-box;
    box-shadow: 0 5px 10px var(--ring-popup-shadow-color);
    border-radius: 3px;
    color: var(--default-font-color);
}

.copy-popup-wrapper > .copy-popup-icon::before {
    content: url("../images/copy-successful-icon.svg");
    padding: 8px;
}

.copy-popup-wrapper > .copy-popup-icon {
    position: relative;
    top: 3px;
}

.copy-popup-wrapper.popup-to-left {
    /* since it is in position absolute we can just move it to the left to make it always appear on the left side of the icon */
    left: -15em;
}

.table-row:hover .copy-popup-wrapper.active-popup,
.sample-container:hover .copy-popup-wrapper.active-popup {
    display: flex !important;
}

.copy-popup-wrapper:hover {
    font-weight: normal;
}

.copy-popup-wrapper > span:last-child {
    padding-right: 14px;
}

.symbol .top-right-position, .sample-container .top-right-position {
    /* it is important for a parent to have a position: relative */
    position: absolute;
    top: 8px;
    right: 8px;
    overflow-wrap: break-word;
    word-break: break-word;
}

.brief {
    white-space: pre-wrap;
    overflow: hidden;
}

h1.cover {
    font-size: 52px;
    line-height: 56px;
    letter-spacing: -1.5px;
    margin-bottom: 0;
    padding-bottom: 32px;
    display: block;
}

@media (max-width: 1119px) {
    h1.cover {
        font-size: 48px;
        line-height: 48px;
        padding-bottom: 8px;
    }
}

@media (max-width: 759px) {
    h1.cover {
        font-size: 32px;
        line-height: 32px;
    }
}

.UnderCoverText {
    font-size: 16px;
    line-height: 28px;
}

.UnderCoverText code {
    font-size: inherit;
}

.UnderCoverText table {
    margin: 8px 0 8px 0;
    word-break: break-word;
}

@media (max-width: 960px) {
    .UnderCoverText table {
        display: block;
        word-break: normal;
        overflow: auto;
    }
}

.main-content a:not([data-name]) {
    padding-bottom: 2px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
    transition: color .1s, border-color .1s;
    overflow-wrap: break-word;
    word-break: break-word;
}

.main-content a:hover {
    border-bottom-color: unset;
    color: inherit
}

a small {
    font-size: 11px;
    margin-top: -0.6em;
    display: block;
}

p.paragraph img {
    display: block;
}

.deprecation-content {
    margin: 20px 10px;
    border: 1px solid var(--border-color);
    padding: 13px 15px 16px 15px;
}

.deprecation-content > h3 {
    margin-top: 0;
    margin-bottom: 0;
}

.deprecation-content > h4 {
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 0;
}

.deprecation-content code.block {
    padding: 5px 10px;
    display: inline-block;
}

.deprecation-content .footnote {
    margin-left: 25px;
    font-size: 13px;
    font-weight: bold;
    display: block;
}

.deprecation-content .footnote > p {
    margin: 0;
}

[data-filterable-current=''] {
    display: none !important;
}

td.content {
    padding-left: 24px;
    padding-top: 16px;
    display: flex;
    flex-direction: column;
}

.main-subrow {
    display: flex;
    flex-direction: row;
    padding: 0;
    flex-wrap: wrap;
}

.main-subrow > div {
    margin-bottom: 8px;
}

.main-subrow > div > span {
    display: flex;
    position: relative;
}

.js .main-subrow:hover .anchor-icon {
    opacity: 1;
    transition: 0.2s;
}

.main-subrow .anchor-icon {
    opacity: 0;
    transition: 0.2s 0.5s;
}

.main-subrow .anchor-icon::before {
    content: url("../images/anchor-copy-button.svg");
}

.main-subrow .anchor-icon:hover {
    cursor: pointer;
}

.main-subrow .anchor-icon:hover > svg path {
    fill: var(--hover-link-color);
}

@media (hover: none) {
    .main-subrow .anchor-icon {
        display: none;
    }
}

.main-subrow .anchor-wrapper {
    position: relative;
    width: 24px;
    height: 16px;
    margin-left: 3px;
}

.inline-flex {
    display: inline-flex;
}

/* Work around an issue: https://github.com/JetBrains/kotlin-playground/issues/91
Applies for main description blocks with platform tabs.
Just in case of possible performance degradation it excluding tabs with briefs on classlike page */
#content > div:not(.tabbedcontent) .sourceset-dependent-content:not([data-active]) {
    display: block !important;
    visibility: hidden;
    height: 0;
    position: fixed;
    top: 0;
}

.with-platform-tags {
    display: flex;
}

.with-platform-tags ~ .main-subrow {
    padding-top: 8px;
}

.cover .with-platform-tabs {
    font-size: var(--default-font-size);
}

.cover > .with-platform-tabs > .content {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
}

.cover > .block {
    padding-top: 48px;
    padding-bottom: 24px;
    font-size: 18px;
    line-height: 28px;
}

.cover > .block:empty {
    padding-bottom: 0;
}

.parameters.wrapped > .parameter {
    display: block;
}

.table-row .inline-comment {
    padding-top: 8px;
    padding-bottom: 8px;
}

.table-row .platform-hinted .sourceset-dependent-content .brief,
.table-row .platform-hinted .sourceset-dependent-content .inline-comment {
    padding: 8px;
}

.table {
    display: flex;
    flex-direction: column;
}

.table-row {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid var(--border-color);
    padding: 11px 0 12px 0;
    background-color: var(--background-color);
}

.table-row:last-of-type {
    border-bottom: none;
}

.table-row .brief-comment {
    color: var(--brief-color);
}

.platform-dependent-row {
    display: grid;
    padding-top: 8px;
}

.title-row {
    display: grid;
    grid-template-columns: auto auto 7em;
    width: 100%;
}

@media print, (min-width: 960px) {
    .title-row {
        grid-template-columns: 20% auto 7em;
    }
}

.keyValue {
    display: grid;
    grid-gap: 8px;
}

@media print, (min-width: 960px) {
    .keyValue {
        grid-template-columns: 20% 80%;
    }

    .keyValue > div:first-child {
        word-break: break-word;
    }
}

@media print, (max-width: 960px) {
    div.wrapper {
        width: auto;
        margin: 0;
    }

    header, section, footer {
        float: none;
        position: static;
        width: auto;
    }

    header {
        padding-right: 320px;
    }

    section {
        border: 1px solid #e5e5e5;
        border-width: 1px 0;
        padding: 20px 0;
        margin: 0 0 20px;
    }

    header a small {
        display: inline;
    }

    header ul {
        position: absolute;
        right: 50px;
        top: 52px;
    }
}

.anchor-highlight {
    border: 1px solid var(--hover-link-color) !important;
    box-shadow: 0 0 0 0.2em #c8e1ff;
    margin-top: 0.2em;
    margin-bottom: 0.2em;
}

.filtered-message {
    margin: 25px;
    font-size: 20px;
    font-weight: bolder;
}

div.runnablesample {
    height: fit-content;
}

/* --- footer --- */
.footer {
    clear: both;
    display: flex;
    align-items: center;
    position: relative;
    min-height: var(--footer-height);
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0.2px;
    color: var(--footer-font-color);
    margin-top: auto;
    background-color: var(--footer-background);
}

.footer--button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--footer-go-to-top-color);
    background-repeat: no-repeat;
    background-position: 50% 50%;
    padding: 0;
    border: none;
    cursor: pointer;
    font-size: 0;
    line-height: 0;
    transition: background-color 200ms ease-in-out;
    will-change: background-color;
}

.footer--button:hover {
    opacity: 0.9;
}

.footer--button_go-to-top {
    background-image: url("../images/go-to-top-icon.svg");
    margin-left: var(--horizontal-spacing-for-content);
    margin-right: 8px;
}

.footer--link {
    display: inline-flex;
    align-items: center;
    color: var(--breadcrumb-font-color);
}

.footer--link_external:after {
    content: '';
    width: 12px;
    height: 12px;
    margin-left: 4px;
    margin-right: var(--horizontal-spacing-for-content);
    background-image: url("../images/footer-go-to-link.svg");
    background-repeat: no-repeat;
    background-position: 50% 50%;
}

/* /--- footer --- */

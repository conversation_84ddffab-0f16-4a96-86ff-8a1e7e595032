//
// Created by <PERSON><PERSON> on 2024/12/4.
//

#ifndef CROWDTRACKERFORKEY_H
#define CROWDTRACKERFORKEY_H
#include "IStrategy.h"
#include "MOT.h"
#include "XbotgoStrategy.h"
#include "XbotgoStrategyCommon.h"
#include "chamCalibrateXY.h"
#include <algorithm>
#include <cmath>
#include <map>
#include <vector>
namespace XbotgoSDK
{

class CrowdTrackerForKey
{
private:
    int frameInterval_;
    int crowdThreshold_;
    int fastPeopleThreshold_;
    int stayTrackResultTimes_;
    float centerPointX_;
    float centerPointY_;

    TrackResult lastPoint;
    std::unique_ptr<MOT> mot;
    std::vector<std::vector<MOTResult>> motResults;

private:
    int calculatePeopleDistanceInQueue(const std::vector<std::vector<MOTResult>>& motResults, std::vector<DelatTrackInfo>& delatTrackInfoResults);
    TrackResult calculateTrackPoint(const std::vector<DelatTrackInfo> delatTrackInfoResults);
    int dynamicFastPeopleFilter(const std::vector<DelatTrackInfo>& delatTrackInfoResults, std::vector<DelatTrackInfo>& filterTrackedResults);
    TrackResult calculateFastTrackPoint(const std::vector<DelatTrackInfo>& trackResults);

public:
    CrowdTrackerForKey(int frameInterval, int crowdThreshold, int fastPeopleThreshold, int motFPS)
        : frameInterval_(frameInterval),
          crowdThreshold_(crowdThreshold),
          fastPeopleThreshold_(fastPeopleThreshold),
          stayTrackResultTimes_(0),
          centerPointX_(0.0f),
          centerPointY_(0.0f),
          lastPoint({0, 0}),
          mot(std::make_unique<MOT>(motFPS)){
          }
    ~CrowdTrackerForKey(){}

    void restart();
    void motClear();
    TrackResult TrackCrowd(std::vector<YOLODetectionResult>& yoloDetectResultFiltered, float centerPointX, float centerPointY);
};

}

#endif // CROWDTRACKERFORKEY_H

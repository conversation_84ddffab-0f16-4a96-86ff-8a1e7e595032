//
//  BLSrtSocketClient.m
//  LivePush
//
//  Created by ji peng on 2024/10/17.
//

#include "BLSRTClient.hpp"
#include "RTPPacket.hpp"
#include "srt.h"
#include "BLUtils.hpp"
#include <chrono>
#define PORT 61017
#include "BLRTCLog.h"
#define NOW (std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now().time_since_epoch()).count())

namespace rtcpushsdk
{

BLSRTClient::BLSRTClient():connected(false)
{
    LOG("BLSRTClient::BLSRTClient() ");
}

BLSRTClient::~BLSRTClient()
{
    LOG("BLSRTClient::~BLSRTClient() ");
}

int BLSRTClient::start(std::string ipAddress)
{
    peerAddress = ipAddress;
    LOG("BLSRTClient::start() ");
    sequenceNumber = 0;
    srt_startup();
    ss = srt_create_socket();
    if (ss == SRT_ERROR){
        srt_close(ss);
        return -1;
    }
    struct sockaddr_in sa;
    uint16_t hostshort = PORT;
    sa.sin_family = AF_INET;
    sa.sin_port = htons(hostshort);
    if (inet_pton(AF_INET, ipAddress.c_str(), &sa.sin_addr) != 1){
        srt_close(ss);
        return -1;
    }
    int yes = 1;
    if (SRT_ERROR == srt_setsockflag(ss, SRTO_SENDER, &yes, sizeof yes)){
        srt_close(ss);
        return -1;
    }
    st = srt_connect(ss, (struct sockaddr*)&sa, sizeof sa);
    if (st == SRT_ERROR){
        LOG("BLSRTClient::srt_client srt_connect error");
        srt_close(ss);
        return -1;
    }
    LOG("BLSRTClient srt_client srt_connect success");
    connected = true;
    return 0;
}

void BLSRTClient::writeData(uint8_t* frame, uint32_t frameSize)
{
    //LOG("BLSRTClient::writeData(uint8_t* frame, uint32_t frameSize)" );
    if(!connected){
        return;
    }
    uint32_t timestamp = NOW;
    uint32_t payloadType = 96;
    // std::vector<uint8_t> naluData(frame,frame + frameSize);
    size_t offset = 0;
    bool isSendError = false;
    while (offset < frameSize) {
        int chunkSize = RTPPacket::min(MTU_SIZE - RTP_HEADER_SIZE, frameSize - offset);
        uint8_t* naluChunk = frame + offset;
        uint8_t *payloadStart = tmpRtpPacket_.data() + RTP_HEADER_SIZE;
        RTPPacket::setRTPHeader(tmpRtpPacket_.data(), payloadType, sequenceNumber++, timestamp);
        // std::vector<uint8_t> naluChunk(naluData.begin() + offset, naluData.begin() + offset + chunkSize);
        if (chunkSize == frameSize) {
            int32_t rtpPacketSize = chunkSize + RTP_HEADER_SIZE;
            std::memcpy(payloadStart, naluChunk, chunkSize);
            size_t bytesSent = srt_sendmsg2(ss, (const char*)tmpRtpPacket_.data(), rtpPacketSize, NULL);
            if (bytesSent<0 || bytesSent>rtpPacketSize) {
                LOG("BLSRTClient::Error sending RTP packet.1 err: %s",srt_getlasterror_str());
                isSendError = true;
                break;
            }
        } else {
            int32_t rtpPacketSize = chunkSize + RTP_HEADER_SIZE + 2;
            //TODO: remove head 4 bytes of payload length when sending on client side,
            //  and add head 4 bytes of payload length when receiving on server side.
            uint8_t fuHeader = 0x1C; //frame[4]; // FU-A类型（高4位是类型0x1C）
            bool start = (offset == 0);
            bool end = (offset + chunkSize >= frameSize);
            if (start) {
                fuHeader |= 0x80; // 设置开始标志
            }
            if (end) {
                fuHeader |= 0x40; // 设置结束标志
            }
            payloadStart[0] = fuHeader; //0x1C;
            payloadStart[1] = frame[4]; //fuHeader;
            std::memcpy(payloadStart + 2, naluChunk, chunkSize);
            // std::vector<uint8_t> fuPacket = {fuHeader, static_cast<uint8_t>(naluChunk[0] & 0x1F)};
            // fuPacket.insert(fuPacket.end(), naluChunk.begin(), naluChunk.end());
            // std::vector<uint8_t>  rtpPacket = RTPPacket::createRTPPacket(fuPacket, timestamp, payloadType, sequenceNumber++);
            size_t bytesSent = srt_sendmsg2(ss, (const char*)tmpRtpPacket_.data(), rtpPacketSize, NULL);
            if (bytesSent<0 || bytesSent>rtpPacketSize) {
                LOG("BLSRTClient::Error sending RTP packet.2 err: %s",srt_getlasterror_str());
                isSendError = true;
                break;
            }
        }
        offset += chunkSize;
    }
//    if(isSendError && !peerAddress.empty()){
//        LOG("BLSRTClient::Error sending RTP packet. reconnect srt server" );
//        stop();
//        start(peerAddress);
//    }
}

void BLSRTClient::readData(uint8_t* frame, uint32_t* frameSize)
{
    
}

void BLSRTClient::stop()
{
    LOG("BLSRTClient::stop() ");
    sequenceNumber = 0;
    connected = false;
    st = srt_close(ss);
    if (st == SRT_ERROR)
    {
        LOG("srt_client srt_close: SRT_ERROR");
    }
    srt_cleanup();
}

bool BLSRTClient::isConnected()
{
    return connected;
}

void BLSRTClient::onBLSRTClientConnectMessage(std::function<void(std::string peerIp ,int status)> func)
{
    sendFunc = func;
}


}
